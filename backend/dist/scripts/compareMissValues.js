"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.compareMissValues = compareMissValues;
const database_1 = require("../config/database");
const dotenv_1 = __importDefault(require("dotenv"));
const path_1 = __importDefault(require("path"));
dotenv_1.default.config({ path: path_1.default.join(__dirname, '../../../.env') });
const excelMissValues = [
    0, 1, 2, 0, 0, 1, 2, 0, 1, 2, 3, 4, 5, 0, 1, 2, 3, 4, 5, 0, 1, 2, 3, 4, 5, 6, 7, 8, 0, 1, 2, 0, 1, 0, 1, 2, 3, 4, 5, 0, 1, 0, 1, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 4, 5, 0, 0, 1, 2, 3, 4, 5, 0, 0, 1, 2, 3, 4, 0, 0, 0, 1, 0, 1, 2, 3, 4, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 0, 1, 2, 3, 4, 5, 6, 0, 1, 0, 1, 2, 0, 1, 0, 0, 1, 2, 3, 0, 0, 1, 0, 0, 1, 2, 0, 1, 2, 3, 0, 0, 1, 2, 3, 4, 5, 0, 1, 2, 3, 4, 5, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 0, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 0, 1, 2, 3, 4, 5, 0, 0, 0, 1, 2, 3, 4, 0, 1, 2, 3, 4, 5, 6, 7, 0, 1, 2, 3, 4, 5, 0, 1, 2, 3, 4, 5, 6, 7, 8, 0, 1, 2, 3, 4, 5, 0, 1, 2, 0, 0, 1, 2, 3, 4, 5, 0, 1, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 4, 0, 1, 0, 1, 2, 0, 0, 1, 0, 1, 2, 3, 4, 5, 6, 0, 1, 2, 0, 1, 2, 3, 4, 5, 6, 7, 0, 1, 2, 3, 4, 5, 6, 7, 8, 0, 1, 2, 3, 4, 5, 6, 0, 1, 0, 1, 2, 0, 1, 0, 1, 2, 3, 4, 5, 6, 7, 0, 1, 2, 3, 0, 1, 2, 3, 4, 5, 6, 0, 0, 0, 0, 0, 1, 2, 3, 0, 1, 2, 3, 4, 0, 0, 0, 1, 2, 3, 4, 5, 6, 0, 0, 1, 0, 1, 2, 3, 0, 0, 1, 2, 3, 4, 0, 0, 1, 2, 0, 1, 2, 0, 1, 0, 0, 1, 2, 0, 0, 1, 2, 0, 1, 2, 3, 4, 5, 6, 0, 0, 1, 0, 1, 2, 3, 0, 0, 1, 0, 0, 1, 2, 0, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 0, 0, 1, 0, 1, 2, 3, 0, 1, 2, 3, 4, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 0, 1, 2, 3, 4, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 0, 1, 2, 0, 1, 2, 3, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 0, 1, 2, 3, 4, 0, 1, 2, 3, 4, 5, 0, 1, 2, 0, 1, 2, 3, 4, 5, 6, 0, 1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 0, 1, 2, 3, 4, 5, 6, 0, 1, 2, 3, 4, 0, 0, 0, 1, 0, 1, 2, 3, 4, 5, 6, 0, 0, 0, 1, 0, 0, 1, 0, 0, 1, 2, 3, 4, 5, 6, 7, 8, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 0, 1, 2, 0, 0, 1, 2, 0, 1, 0, 1, 0, 1, 0, 1, 2, 0, 1, 2, 0, 1, 2, 3, 4, 5, 6, 7, 8, 0, 0, 0, 1, 2, 3, 4, 5, 0, 1, 2, 0, 1, 0, 1, 2, 3, 0, 1, 2, 3, 4, 5, 6, 0, 1, 2, 3, 4, 0, 0, 1, 2, 0, 1, 2, 0, 1, 2, 3, 4, 0, 1, 2, 0, 0, 0, 1, 2, 0, 0, 1, 2, 0, 1, 0, 0, 1, 2, 0, 1, 2, 3, 4, 5, 6, 7, 0, 1, 0, 1, 2, 3, 4, 5, 6, 7, 0, 1, 2, 3, 4, 5, 6, 7, 0, 1, 2, 3, 4, 5, 6, 0, 1, 2, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 0, 1, 0, 0, 1, 0, 0, 1, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 0, 0, 1, 0, 1, 0, 1, 2, 0, 0, 1, 2, 3, 4, 5, 0, 0, 1, 2, 3, 4, 5, 6, 7, 8, 0, 1, 2, 3, 4, 5, 6, 7, 8, 0, 1, 2, 0, 1, 2, 3, 0, 0, 1, 2, 3, 0, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 0, 0, 1, 2, 3, 4, 5, 6, 7, 8, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 0, 1, 2, 3, 4, 5, 0, 1, 2, 3, 0, 1, 0, 1, 0, 1, 2, 0, 1, 0, 1, 0, 1, 2, 3, 4, 5, 0, 0, 1, 2, 0, 1, 2, 3, 4, 5, 0, 0, 1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 0, 1, 2, 0, 0, 1, 2, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 0, 0, 1, 2, 0, 1, 2, 3, 4, 5, 0, 1, 2, 3, 4, 5, 0, 0, 1, 2, 3, 4, 5, 6, 0, 1, 2, 3, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 0, 1, 2, 3, 4, 5, 0, 1, 2, 0, 1, 2, 0, 0, 1, 0, 1, 0, 0, 1, 2, 3, 4, 5, 0, 1, 2, 3, 0, 0, 0, 1, 2, 3, 0, 1, 2, 0, 1, 0, 1, 2, 3, 4, 5, 0, 1, 2, 3, 4, 5, 0, 1, 0, 0, 1, 2, 3, 4, 5, 0, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 0, 0, 1, 2, 3, 4, 5, 6, 7, 8, 0, 1, 2, 3, 0, 1, 2, 0, 1, 2, 3, 4, 0, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 0, 1, 0, 0, 1, 2, 0, 0, 0, 1, 0, 1, 2, 0, 1, 2, 0, 1, 2, 3, 4, 5, 6, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 0, 1, 2, 3, 4, 5, 6, 7, 0, 1, 2, 3, 4, 5, 6, 0, 1, 2, 0, 1, 0, 1, 0, 1, 2, 3, 4, 5, 0, 1, 0, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 0, 1, 0, 1, 2, 0, 0, 1, 0, 1, 0, 0, 1, 0, 0, 0
];
function parseNumbers(numbersStr) {
    if (!numbersStr)
        return [];
    if (numbersStr.includes(',')) {
        return numbersStr.split(',').map(n => parseInt(n.trim())).filter(num => !isNaN(num));
    }
    else {
        return numbersStr.split('').map(n => parseInt(n)).filter(num => !isNaN(num));
    }
}
function calculateAllMissValues(results) {
    const periodsData = [];
    for (let i = 0; i < results.length; i++) {
        const currentResult = results[i];
        if (!currentResult)
            continue;
        const currentNumbers = parseNumbers(currentResult.numbers.join(''));
        const missValues = new Array(10).fill(0);
        for (let digit = 0; digit <= 9; digit++) {
            if (currentNumbers.includes(digit)) {
                missValues[digit] = 0;
            }
            else {
                let lastOccurrenceIndex = -1;
                for (let j = i - 1; j >= 0; j--) {
                    const checkResult = results[j];
                    if (!checkResult)
                        continue;
                    const checkNumbers = parseNumbers(checkResult.numbers.join(''));
                    if (checkNumbers.includes(digit)) {
                        lastOccurrenceIndex = j;
                        break;
                    }
                }
                if (lastOccurrenceIndex >= 0) {
                    missValues[digit] = i - lastOccurrenceIndex;
                }
                else {
                    missValues[digit] = i + 1;
                }
            }
        }
        periodsData.push({
            period: currentResult.period,
            numbers: currentNumbers,
            missValues: missValues
        });
    }
    return periodsData;
}
async function compareMissValues() {
    try {
        console.log('🔄 初始化数据库连接...');
        await database_1.AppDataSource.initialize();
        console.log('✅ 数据库连接成功');
        const rawResults = await database_1.AppDataSource.query(`
      SELECT period, draw_time, numbers
      FROM lottery_results
      WHERE lottery_type_id = 4
      AND period >= '3305976'
      AND period <= '3307133'
      ORDER BY period ASC
    `);
        console.log(`📊 获取到 ${rawResults.length} 期PC28历史数据`);
        const results = rawResults.map((row) => ({
            period: row.period,
            drawTime: row.draw_time,
            numbers: parseNumbers(row.numbers)
        }));
        const periodsData = calculateAllMissValues(results);
        console.log(`📊 计算了 ${periodsData.length} 期的遗漏值`);
        const algorithmMissValues = periodsData.map(period => period.missValues[0]);
        console.log(`\n🎯 遗漏值对比分析:`);
        console.log(`Excel遗漏值数量: ${excelMissValues.length}`);
        console.log(`算法遗漏值数量: ${algorithmMissValues.length}`);
        console.log(`\n📊 前50期遗漏值对比:`);
        console.log(`位置\t期号\t\tExcel\t算法\t匹配`);
        console.log(`${'='.repeat(60)}`);
        let matchCount = 0;
        const compareCount = Math.min(50, excelMissValues.length, algorithmMissValues.length);
        for (let i = 0; i < compareCount; i++) {
            const excelValue = excelMissValues[i];
            const algorithmValue = algorithmMissValues[i];
            if (excelValue === undefined || algorithmValue === undefined)
                continue;
            const match = excelValue === algorithmValue;
            if (match)
                matchCount++;
            const status = match ? '✅' : '❌';
            const period = periodsData[i]?.period || 'N/A';
            console.log(`${(i + 1).toString().padStart(3)}\t${period}\t${excelValue.toString().padStart(2)}\t${algorithmValue.toString().padStart(2)}\t${status}`);
        }
        console.log(`\n前50期匹配率: ${matchCount}/${compareCount} = ${(matchCount / compareCount * 100).toFixed(1)}%`);
        let totalMatches = 0;
        const totalCompare = Math.min(excelMissValues.length, algorithmMissValues.length);
        for (let i = 0; i < totalCompare; i++) {
            if (excelMissValues[i] === algorithmMissValues[i]) {
                totalMatches++;
            }
        }
        console.log(`\n📊 全量对比统计:`);
        console.log(`总匹配数: ${totalMatches}/${totalCompare}`);
        console.log(`总匹配率: ${(totalMatches / totalCompare * 100).toFixed(1)}%`);
        if (totalMatches < totalCompare) {
            console.log(`\n🔍 差异分析:`);
            const differences = [];
            for (let i = 0; i < Math.min(100, totalCompare); i++) {
                if (excelMissValues[i] !== algorithmMissValues[i]) {
                    differences.push(i);
                }
            }
            console.log(`前100期中的差异位置: ${differences.slice(0, 10).join(', ')}${differences.length > 10 ? '...' : ''}`);
            console.log(`\n📋 前10个差异详情:`);
            console.log(`位置\t期号\t\t开奖号码\tExcel\t算法\t差值`);
            console.log(`${'='.repeat(70)}`);
            for (let i = 0; i < Math.min(10, differences.length); i++) {
                const pos = differences[i];
                if (pos === undefined)
                    continue;
                const period = periodsData[pos];
                if (period) {
                    const excelValue = excelMissValues[pos];
                    const algorithmValue = algorithmMissValues[pos];
                    if (excelValue !== undefined && algorithmValue !== undefined) {
                        const diff = excelValue - algorithmValue;
                        console.log(`${(pos + 1).toString().padStart(3)}\t${period.period}\t[${period.numbers.join(',')}]\t\t${excelValue.toString().padStart(2)}\t${algorithmValue.toString().padStart(2)}\t${diff > 0 ? '+' : ''}${diff}`);
                    }
                }
            }
        }
    }
    catch (error) {
        console.error('❌ 对比失败:', error);
    }
    finally {
        if (database_1.AppDataSource.isInitialized) {
            await database_1.AppDataSource.destroy();
            console.log('🔄 数据库连接已关闭');
        }
    }
}
if (require.main === module) {
    compareMissValues().catch(console.error);
}
//# sourceMappingURL=compareMissValues.js.map