"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyMissValueCalculation = verifyMissValueCalculation;
const database_1 = require("../config/database");
const dotenv_1 = __importDefault(require("dotenv"));
const path_1 = __importDefault(require("path"));
dotenv_1.default.config({ path: path_1.default.join(__dirname, '../../../.env') });
function parseNumbers(numbersStr) {
    if (!numbersStr)
        return [];
    if (numbersStr.includes(',')) {
        return numbersStr.split(',').map(n => parseInt(n.trim())).filter(num => !isNaN(num));
    }
    else {
        return numbersStr.split('').map(n => parseInt(n)).filter(num => !isNaN(num));
    }
}
function calculateAllMissValues(results) {
    const periodsData = [];
    for (let i = 0; i < results.length; i++) {
        const currentResult = results[i];
        if (!currentResult)
            continue;
        const missValues = new Array(10).fill(0);
        for (let digit = 0; digit <= 9; digit++) {
            let missCount = 0;
            for (let j = i - 1; j >= 0; j--) {
                const prevResult = results[j];
                if (!prevResult)
                    continue;
                const prevNumbers = parseNumbers(prevResult.numbers.join(''));
                if (prevNumbers.includes(digit)) {
                    break;
                }
                missCount++;
            }
            missValues[digit] = missCount;
        }
        periodsData.push({
            period: currentResult.period,
            numbers: parseNumbers(currentResult.numbers.join('')),
            missValues: missValues
        });
    }
    return periodsData;
}
function buildMissValueArrays(periodsData) {
    const missValueArrays = new Map();
    for (let digit = 0; digit <= 9; digit++) {
        missValueArrays.set(digit, []);
    }
    for (let i = 1; i < periodsData.length; i++) {
        const currentPeriod = periodsData[i];
        const prevPeriod = periodsData[i - 1];
        if (!currentPeriod || !prevPeriod)
            continue;
        const uniqueNumbers = [...new Set(currentPeriod.numbers)];
        for (const digit of uniqueNumbers) {
            const prevMissValue = prevPeriod.missValues[digit];
            if (prevMissValue !== undefined) {
                const missArray = missValueArrays.get(digit);
                missArray.push(prevMissValue);
            }
        }
    }
    return missValueArrays;
}
async function verifyMissValueCalculation() {
    try {
        console.log('🔄 初始化数据库连接...');
        await database_1.AppDataSource.initialize();
        console.log('✅ 数据库连接成功');
        const rawResults = await database_1.AppDataSource.query(`
      SELECT period, draw_time, numbers
      FROM lottery_results
      WHERE lottery_type_id = 4
      AND period >= '3305976'
      AND period <= '3307133'
      ORDER BY period ASC
    `);
        console.log(`📊 获取到 ${rawResults.length} 期PC28历史数据`);
        const results = rawResults.map((row) => ({
            period: row.period,
            drawTime: row.draw_time,
            numbers: parseNumbers(row.numbers)
        }));
        const periodsData = calculateAllMissValues(results);
        console.log(`📊 计算了 ${periodsData.length} 期的遗漏值`);
        const missValueArrays = buildMissValueArrays(periodsData);
        const digit0Array = missValueArrays.get(0);
        console.log(`\n🎯 号码0的遗漏值数组:`);
        console.log(`   数组长度: ${digit0Array.length}`);
        console.log(`   数组内容: [${digit0Array.join(',')}]`);
        const zeroCount = digit0Array.filter(val => val === 0).length;
        console.log(`   遗漏值0出现次数: ${zeroCount}`);
        console.log(`\n📋 最后10期的详细信息:`);
        const lastTenPeriods = periodsData.slice(-10);
        lastTenPeriods.forEach((period) => {
            console.log(`   期号${period.period}: 号码[${period.numbers.join(',')}], 号码0遗漏值=${period.missValues[0]}`);
        });
        const latestPeriod = periodsData[periodsData.length - 1];
        if (latestPeriod) {
            console.log(`\n🎯 最新期信息:`);
            console.log(`   期号: ${latestPeriod.period}`);
            console.log(`   开奖号码: [${latestPeriod.numbers.join(',')}]`);
            console.log(`   当前遗漏值: ${latestPeriod.missValues.map((miss, digit) => `${digit}:${miss}`).join(', ')}`);
        }
        console.log(`\n📊 与Excel数据对比:`);
        console.log(`   Excel统计: 号码0遗漏值数组有76个元素`);
        console.log(`   算法计算: 号码0遗漏值数组有${digit0Array.length}个元素`);
        console.log(`   Excel统计: 遗漏值0出现76次`);
        console.log(`   算法计算: 遗漏值0出现${zeroCount}次`);
        if (digit0Array.length === 76 && zeroCount === 76) {
            console.log(`   ✅ 算法计算结果与Excel数据一致`);
        }
        else {
            console.log(`   ❌ 算法计算结果与Excel数据不一致`);
        }
    }
    catch (error) {
        console.error('❌ 验证失败:', error);
    }
    finally {
        if (database_1.AppDataSource.isInitialized) {
            await database_1.AppDataSource.destroy();
            console.log('🔄 数据库连接已关闭');
        }
    }
}
if (require.main === module) {
    verifyMissValueCalculation().catch(console.error);
}
//# sourceMappingURL=verifyMissValueCalculation.js.map