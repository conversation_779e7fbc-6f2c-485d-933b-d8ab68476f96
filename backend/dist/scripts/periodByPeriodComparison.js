"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.periodByPeriodComparison = periodByPeriodComparison;
const database_1 = require("../config/database");
const dotenv_1 = __importDefault(require("dotenv"));
const path_1 = __importDefault(require("path"));
dotenv_1.default.config({ path: path_1.default.join(__dirname, '../../../.env') });
const excelMissValueArray = [
    0, 2, 0, 2, 5, 5, 8, 2, 1, 5, 1, 1, 3, 3, 5, 0, 5, 0, 4, 0, 0, 1, 4, 21, 6, 1, 2, 1, 0, 3, 0, 1, 0, 2, 3, 0, 5, 5, 9, 6, 0, 3, 3, 2, 5, 0, 0, 4, 7, 5, 8, 5, 2, 0, 5, 1, 2, 2, 2, 1, 1, 1, 4, 1, 2, 0, 1, 6, 2, 7, 8, 6, 1, 2, 1, 7, 3, 6, 0, 0, 0, 0, 3, 4, 0, 0, 6, 0, 1, 3, 0, 4, 0, 2, 2, 1, 0, 2, 0, 2, 6, 0, 1, 3, 0, 1, 0, 2, 0, 9, 4, 0, 1, 3, 4, 16, 11, 4, 2, 2, 2, 0, 2, 3, 16, 9, 0, 4, 5, 2, 6, 1, 8, 6, 4, 0, 0, 1, 6, 0, 0, 1, 0, 1, 0, 8, 11, 2, 0, 2, 1, 1, 1, 2, 2, 8, 0, 0, 5, 2, 1, 3, 6, 4, 0, 2, 2, 4, 2, 0, 0, 2, 0, 2, 1, 0, 2, 7, 1, 7, 7, 6, 2, 16, 1, 0, 1, 0, 1, 3, 3, 1, 0, 1, 1, 2, 0, 5, 0, 8, 8, 2, 3, 0, 3, 0, 9, 1, 0, 8, 14, 5, 3, 1, 1, 2, 1, 1, 5, 0, 2, 5, 0, 1, 9, 1, 2, 0, 2, 10, 0, 2, 5, 5, 0, 6, 3, 19, 5, 2, 2, 0, 1, 1, 0, 5, 3, 0, 0, 3, 2, 1, 5, 5, 1, 0, 5, 0, 9, 1, 0, 8, 3, 2, 4, 0, 12, 1, 0, 2, 0, 0, 1, 2, 2, 6, 9, 6, 7, 6, 2, 1, 1, 5, 1, 0, 16, 1, 2, 0, 1, 1, 0, 1, 0, 0
];
function parseNumbers(numbersStr) {
    if (!numbersStr)
        return [];
    if (numbersStr.includes(',')) {
        return numbersStr.split(',').map(n => parseInt(n.trim())).filter(num => !isNaN(num));
    }
    else {
        return numbersStr.split('').map(n => parseInt(n)).filter(num => !isNaN(num));
    }
}
function calculateAllMissValues(results) {
    const periodsData = [];
    for (let i = 0; i < results.length; i++) {
        const currentResult = results[i];
        if (!currentResult)
            continue;
        const missValues = new Array(10).fill(0);
        for (let digit = 0; digit <= 9; digit++) {
            let missCount = 0;
            for (let j = i - 1; j >= 0; j--) {
                const prevResult = results[j];
                if (!prevResult)
                    continue;
                const prevNumbers = parseNumbers(prevResult.numbers.join(''));
                if (prevNumbers.includes(digit)) {
                    break;
                }
                missCount++;
            }
            missValues[digit] = missCount;
        }
        periodsData.push({
            period: currentResult.period,
            numbers: parseNumbers(currentResult.numbers.join('')),
            missValues: missValues
        });
    }
    return periodsData;
}
async function periodByPeriodComparison() {
    try {
        console.log('🔄 初始化数据库连接...');
        await database_1.AppDataSource.initialize();
        console.log('✅ 数据库连接成功');
        const rawResults = await database_1.AppDataSource.query(`
      SELECT period, draw_time, numbers
      FROM lottery_results
      WHERE lottery_type_id = 4
      AND period >= '3305976'
      AND period <= '3307133'
      ORDER BY period ASC
    `);
        console.log(`📊 获取到 ${rawResults.length} 期PC28历史数据`);
        const results = rawResults.map((row) => ({
            period: row.period,
            drawTime: row.draw_time,
            numbers: parseNumbers(row.numbers)
        }));
        const periodsData = calculateAllMissValues(results);
        console.log(`📊 计算了 ${periodsData.length} 期的遗漏值`);
        const digit0MissArray = [];
        for (let i = 0; i < periodsData.length; i++) {
            const currentPeriod = periodsData[i];
            if (!currentPeriod)
                continue;
            const uniqueNumbers = [...new Set(currentPeriod.numbers)];
            if (uniqueNumbers.includes(0)) {
                if (i === 0) {
                    digit0MissArray.push(0);
                }
                else {
                    const prevPeriod = periodsData[i - 1];
                    if (prevPeriod) {
                        digit0MissArray.push(prevPeriod.missValues[0]);
                    }
                }
            }
        }
        console.log(`\n🎯 修正后的对比分析:`);
        console.log(`📋 Excel数据长度: ${excelMissValueArray.length}`);
        console.log(`📋 算法数据长度: ${digit0MissArray.length}`);
        console.log(`\n📊 前30个元素详细对比:`);
        for (let i = 0; i < Math.min(30, excelMissValueArray.length, digit0MissArray.length); i++) {
            const excelValue = excelMissValueArray[i];
            const algorithmValue = digit0MissArray[i];
            const match = excelValue === algorithmValue ? '✅' : '❌';
            console.log(`位置${i.toString().padStart(2)}: Excel=${excelValue.toString().padStart(2)}, 算法=${algorithmValue.toString().padStart(2)} ${match}`);
        }
        console.log(`\n📋 前10期开出号码0的详情:`);
        let count = 0;
        for (let i = 0; i < periodsData.length && count < 10; i++) {
            const period = periodsData[i];
            if (period && period.numbers.includes(0)) {
                const prevMissValue = i === 0 ? 0 : (periodsData[i - 1]?.missValues[0] || 0);
                console.log(`期号${period.period}: 号码[${period.numbers.join(',')}], 上期遗漏=${prevMissValue}, Excel第${count + 1}个=${excelMissValueArray[count]}, 算法第${count + 1}个=${digit0MissArray[count]}`);
                count++;
            }
        }
        const isCompleteMatch = JSON.stringify(excelMissValueArray) === JSON.stringify(digit0MissArray);
        console.log(`\n🎯 完全匹配检查: ${isCompleteMatch ? '✅ 完全一致' : '❌ 存在差异'}`);
        if (!isCompleteMatch && excelMissValueArray.length === digit0MissArray.length) {
            const differences = [];
            for (let i = 0; i < excelMissValueArray.length; i++) {
                if (excelMissValueArray[i] !== digit0MissArray[i]) {
                    differences.push(i);
                }
            }
            console.log(`📊 不匹配位置数量: ${differences.length}`);
            if (differences.length <= 10) {
                console.log(`📋 不匹配位置: ${differences.join(', ')}`);
            }
        }
    }
    catch (error) {
        console.error('❌ 对比分析失败:', error);
    }
    finally {
        if (database_1.AppDataSource.isInitialized) {
            await database_1.AppDataSource.destroy();
            console.log('🔄 数据库连接已关闭');
        }
    }
}
if (require.main === module) {
    periodByPeriodComparison().catch(console.error);
}
//# sourceMappingURL=periodByPeriodComparison.js.map