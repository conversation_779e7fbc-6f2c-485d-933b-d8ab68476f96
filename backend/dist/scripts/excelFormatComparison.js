"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.excelFormatComparison = excelFormatComparison;
const database_1 = require("../config/database");
const dotenv_1 = __importDefault(require("dotenv"));
const path_1 = __importDefault(require("path"));
dotenv_1.default.config({ path: path_1.default.join(__dirname, '../../../.env') });
const excelData = [
    { numbers: '405', missValue: 0 },
    { numbers: '894', missValue: 1 },
    { numbers: '139', missValue: 2 },
    { numbers: '006', missValue: 0 },
    { numbers: '065', missValue: 0 },
    { numbers: '156', missValue: 1 },
    { numbers: '519', missValue: 2 },
    { numbers: '082', missValue: 0 },
    { numbers: '196', missValue: 1 },
    { numbers: '372', missValue: 2 },
    { numbers: '233', missValue: 3 },
    { numbers: '276', missValue: 4 },
    { numbers: '579', missValue: 5 },
    { numbers: '021', missValue: 0 },
    { numbers: '153', missValue: 1 },
    { numbers: '163', missValue: 2 },
    { numbers: '481', missValue: 3 },
    { numbers: '843', missValue: 4 },
    { numbers: '586', missValue: 5 },
    { numbers: '700', missValue: 0 }
];
function parseNumbers(numbersStr) {
    if (!numbersStr)
        return [];
    if (numbersStr.includes(',')) {
        return numbersStr.split(',').map(n => parseInt(n.trim())).filter(num => !isNaN(num));
    }
    else {
        return numbersStr.split('').map(n => parseInt(n)).filter(num => !isNaN(num));
    }
}
function calculateAllMissValues(results) {
    const periodsData = [];
    for (let i = 0; i < results.length; i++) {
        const currentResult = results[i];
        if (!currentResult)
            continue;
        const missValues = new Array(10).fill(0);
        for (let digit = 0; digit <= 9; digit++) {
            let missCount = 0;
            for (let j = i - 1; j >= 0; j--) {
                const prevResult = results[j];
                if (!prevResult)
                    continue;
                const prevNumbers = parseNumbers(prevResult.numbers.join(''));
                if (prevNumbers.includes(digit)) {
                    break;
                }
                missCount++;
            }
            missValues[digit] = missCount;
        }
        periodsData.push({
            period: currentResult.period,
            numbers: parseNumbers(currentResult.numbers.join('')),
            missValues: missValues
        });
    }
    return periodsData;
}
async function excelFormatComparison() {
    try {
        console.log('🔄 初始化数据库连接...');
        await database_1.AppDataSource.initialize();
        console.log('✅ 数据库连接成功');
        const rawResults = await database_1.AppDataSource.query(`
      SELECT period, draw_time, numbers
      FROM lottery_results
      WHERE lottery_type_id = 4
      AND period >= '3305976'
      ORDER BY period ASC
      LIMIT 20
    `);
        console.log(`📊 获取到 ${rawResults.length} 期PC28历史数据`);
        const results = rawResults.map((row) => ({
            period: row.period,
            drawTime: row.draw_time,
            numbers: parseNumbers(row.numbers)
        }));
        const periodsData = calculateAllMissValues(results);
        console.log(`📊 计算了 ${periodsData.length} 期的遗漏值`);
        console.log(`\n🎯 Excel格式对比分析:`);
        console.log(`期号\t\t数据库号码\tExcel号码\t数据库0遗漏\tExcel0遗漏\t匹配`);
        console.log(`${'='.repeat(80)}`);
        for (let i = 0; i < Math.min(20, periodsData.length, excelData.length); i++) {
            const period = periodsData[i];
            const excel = excelData[i];
            if (!period || !excel)
                continue;
            const dbNumbers = period.numbers.join('');
            const excelNumbers = excel.numbers;
            const dbMissValue = period.missValues[0];
            const excelMissValue = excel.missValue;
            const numbersMatch = dbNumbers === excelNumbers ? '✅' : '❌';
            const missValueMatch = dbMissValue === excelMissValue ? '✅' : '❌';
            console.log(`${period.period}\t${dbNumbers}\t\t${excelNumbers}\t\t${dbMissValue}\t\t${excelMissValue}\t\t${numbersMatch}${missValueMatch}`);
        }
        console.log(`\n📊 包含号码0的期次分析:`);
        let count = 0;
        for (let i = 0; i < Math.min(20, periodsData.length); i++) {
            const period = periodsData[i];
            const excel = excelData[i];
            if (period && period.numbers.includes(0) && excel) {
                count++;
                console.log(`第${count}次: 期号${period.period}, 号码[${period.numbers.join(',')}], 算法遗漏=${period.missValues[0]}, Excel遗漏=${excel.missValue}`);
            }
        }
        console.log(`\n🔍 数据排序检查:`);
        let isSorted = true;
        for (let i = 1; i < periodsData.length; i++) {
            const prevData = periodsData[i - 1];
            const currData = periodsData[i];
            if (prevData && currData) {
                const prevPeriod = parseInt(prevData.period);
                const currPeriod = parseInt(currData.period);
                if (prevPeriod >= currPeriod) {
                    isSorted = false;
                    console.log(`❌ 排序错误: ${prevData.period} -> ${currData.period}`);
                }
            }
        }
        if (isSorted) {
            console.log(`✅ 数据按期号正确升序排列`);
        }
    }
    catch (error) {
        console.error('❌ 对比分析失败:', error);
    }
    finally {
        if (database_1.AppDataSource.isInitialized) {
            await database_1.AppDataSource.destroy();
            console.log('🔄 数据库连接已关闭');
        }
    }
}
if (require.main === module) {
    excelFormatComparison().catch(console.error);
}
//# sourceMappingURL=excelFormatComparison.js.map