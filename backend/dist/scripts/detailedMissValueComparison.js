"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.detailedMissValueComparison = detailedMissValueComparison;
const database_1 = require("../config/database");
const dotenv_1 = __importDefault(require("dotenv"));
const path_1 = __importDefault(require("path"));
dotenv_1.default.config({ path: path_1.default.join(__dirname, '../../../.env') });
const excelMissValueArray = [
    0, 2, 0, 2, 5, 5, 8, 2, 1, 5, 1, 1, 3, 3, 5, 0, 5, 0, 4, 0, 0, 1, 4, 21, 6, 1, 2, 1, 0, 3, 0, 1, 0, 2, 3, 0, 5, 5, 9, 6, 0, 3, 3, 2, 5, 0, 0, 4, 7, 5, 8, 5, 2, 0, 5, 1, 2, 2, 2, 1, 1, 1, 4, 1, 2, 0, 1, 6, 2, 7, 8, 6, 1, 2, 1, 7, 3, 6, 0, 0, 0, 0, 3, 4, 0, 0, 6, 0, 1, 3, 0, 4, 0, 2, 2, 1, 0, 2, 0, 2, 6, 0, 1, 3, 0, 1, 0, 2, 0, 9, 4, 0, 1, 3, 4, 16, 11, 4, 2, 2, 2, 0, 2, 3, 16, 9, 0, 4, 5, 2, 6, 1, 8, 6, 4, 0, 0, 1, 6, 0, 0, 1, 0, 1, 0, 8, 11, 2, 0, 2, 1, 1, 1, 2, 2, 8, 0, 0, 5, 2, 1, 3, 6, 4, 0, 2, 2, 4, 2, 0, 0, 2, 0, 2, 1, 0, 2, 7, 1, 7, 7, 6, 2, 16, 1, 0, 1, 0, 1, 3, 3, 1, 0, 1, 1, 2, 0, 5, 0, 8, 8, 2, 3, 0, 3, 0, 9, 1, 0, 8, 14, 5, 3, 1, 1, 2, 1, 1, 5, 0, 2, 5, 0, 1, 9, 1, 2, 0, 2, 10, 0, 2, 5, 5, 0, 6, 3, 19, 5, 2, 2, 0, 1, 1, 0, 5, 3, 0, 0, 3, 2, 1, 5, 5, 1, 0, 5, 0, 9, 1, 0, 8, 3, 2, 4, 0, 12, 1, 0, 2, 0, 0, 1, 2, 2, 6, 9, 6, 7, 6, 2, 1, 1, 5, 1, 0, 16, 1, 2, 0, 1, 1, 0, 1, 0, 0
];
function parseNumbers(numbersStr) {
    if (!numbersStr)
        return [];
    if (numbersStr.includes(',')) {
        return numbersStr.split(',').map(n => parseInt(n.trim())).filter(num => !isNaN(num));
    }
    else {
        return numbersStr.split('').map(n => parseInt(n)).filter(num => !isNaN(num));
    }
}
function calculateAllMissValues(results) {
    const periodsData = [];
    for (let i = 0; i < results.length; i++) {
        const currentResult = results[i];
        if (!currentResult)
            continue;
        const missValues = new Array(10).fill(0);
        for (let digit = 0; digit <= 9; digit++) {
            let missCount = 0;
            for (let j = i - 1; j >= 0; j--) {
                const prevResult = results[j];
                if (!prevResult)
                    continue;
                const prevNumbers = parseNumbers(prevResult.numbers.join(''));
                if (prevNumbers.includes(digit)) {
                    break;
                }
                missCount++;
            }
            missValues[digit] = missCount;
        }
        periodsData.push({
            period: currentResult.period,
            numbers: parseNumbers(currentResult.numbers.join('')),
            missValues: missValues
        });
    }
    return periodsData;
}
function buildMissValueArrays(periodsData) {
    const missValueArrays = new Map();
    for (let digit = 0; digit <= 9; digit++) {
        missValueArrays.set(digit, []);
    }
    for (let i = 0; i < periodsData.length; i++) {
        const currentPeriod = periodsData[i];
        if (!currentPeriod)
            continue;
        const uniqueNumbers = [...new Set(currentPeriod.numbers)];
        for (const digit of uniqueNumbers) {
            const missArray = missValueArrays.get(digit);
            if (i === 0) {
                missArray.push(0);
            }
            else {
                const prevPeriod = periodsData[i - 1];
                if (prevPeriod) {
                    const prevMissValue = prevPeriod.missValues[digit];
                    if (prevMissValue !== undefined) {
                        missArray.push(prevMissValue);
                    }
                }
            }
        }
    }
    return missValueArrays;
}
async function detailedMissValueComparison() {
    try {
        console.log('🔄 初始化数据库连接...');
        await database_1.AppDataSource.initialize();
        console.log('✅ 数据库连接成功');
        const rawResults = await database_1.AppDataSource.query(`
      SELECT period, draw_time, numbers
      FROM lottery_results
      WHERE lottery_type_id = 4
      AND period >= '3305976'
      AND period <= '3307133'
      ORDER BY period ASC
    `);
        console.log(`📊 获取到 ${rawResults.length} 期PC28历史数据`);
        const results = rawResults.map((row) => ({
            period: row.period,
            drawTime: row.draw_time,
            numbers: parseNumbers(row.numbers)
        }));
        const periodsData = calculateAllMissValues(results);
        console.log(`📊 计算了 ${periodsData.length} 期的遗漏值`);
        const missValueArrays = buildMissValueArrays(periodsData);
        const digit0Array = missValueArrays.get(0);
        console.log(`\n🎯 详细对比分析:`);
        console.log(`📋 Excel数据长度: ${excelMissValueArray.length}`);
        console.log(`📋 算法数据长度: ${digit0Array.length}`);
        console.log(`\n📊 前20个元素对比:`);
        console.log(`Excel: [${excelMissValueArray.slice(0, 20).join(',')}]`);
        console.log(`算法:  [${digit0Array.slice(0, 20).join(',')}]`);
        console.log(`\n🔍 检查起始差异:`);
        console.log(`Excel第1个元素: ${excelMissValueArray[0]}`);
        console.log(`算法第1个元素: ${digit0Array[0]}`);
        const algorithmWithZero = [0, ...digit0Array];
        const isMatch = JSON.stringify(excelMissValueArray.slice(0, 20)) === JSON.stringify(algorithmWithZero.slice(0, 20));
        console.log(`Excel数据是否等于算法数据前加0: ${isMatch ? '✅ 是' : '❌ 否'}`);
        if (periodsData.length > 0) {
            const firstPeriod = periodsData[0];
            if (firstPeriod) {
                console.log(`\n📋 第一期详细信息:`);
                console.log(`   期号: ${firstPeriod.period}`);
                console.log(`   开奖号码: [${firstPeriod.numbers.join(',')}]`);
                console.log(`   号码0遗漏值: ${firstPeriod.missValues[0]}`);
                console.log(`   是否包含号码0: ${firstPeriod.numbers.includes(0) ? '是' : '否'}`);
            }
        }
        if (periodsData.length > 1) {
            const secondPeriod = periodsData[1];
            if (secondPeriod) {
                console.log(`\n📋 第二期详细信息:`);
                console.log(`   期号: ${secondPeriod.period}`);
                console.log(`   开奖号码: [${secondPeriod.numbers.join(',')}]`);
                console.log(`   号码0遗漏值: ${secondPeriod.missValues[0]}`);
                console.log(`   是否包含号码0: ${secondPeriod.numbers.includes(0) ? '是' : '否'}`);
                if (secondPeriod.numbers.includes(0) && periodsData[0]) {
                    console.log(`   ✅ 第二期开出号码0，记录上期遗漏值: ${periodsData[0].missValues[0]}`);
                }
            }
        }
        const periodsWithZero = periodsData.filter((period, index) => index > 0 && period.numbers.includes(0));
        console.log(`\n📊 开出号码0的期次统计:`);
        console.log(`   总共开出次数: ${periodsWithZero.length}`);
        console.log(`   算法记录次数: ${digit0Array.length}`);
        console.log(`   Excel记录次数: ${excelMissValueArray.length}`);
    }
    catch (error) {
        console.error('❌ 对比分析失败:', error);
    }
    finally {
        if (database_1.AppDataSource.isInitialized) {
            await database_1.AppDataSource.destroy();
            console.log('🔄 数据库连接已关闭');
        }
    }
}
if (require.main === module) {
    detailedMissValueComparison().catch(console.error);
}
//# sourceMappingURL=detailedMissValueComparison.js.map