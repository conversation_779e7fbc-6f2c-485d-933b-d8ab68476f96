"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyNumber8MissValues = verifyNumber8MissValues;
const database_1 = require("../config/database");
const dotenv_1 = __importDefault(require("dotenv"));
const path_1 = __importDefault(require("path"));
dotenv_1.default.config({ path: path_1.default.join(__dirname, '../../../.env') });
const excelNumber8OpenMissValues = [
    0, 1, 5, 8, 0, 0, 4, 0, 2, 1, 10, 6, 0, 1, 3, 4, 5, 1, 9, 0, 5, 9, 9, 6, 4, 1, 10, 3, 1, 0, 10, 0, 1, 2, 2, 3, 0, 1, 10, 0, 1, 1, 0, 0, 3, 1, 0, 0, 10, 0, 2, 1, 2, 0, 6, 1, 7, 0, 0, 0, 1, 10, 0, 2, 4, 6, 4, 3, 0, 1, 8, 0, 3, 5, 2, 4, 0, 0, 3, 3, 2, 2, 7, 1, 5, 12, 8, 3, 2, 0, 0, 2, 11, 8, 1, 1, 7, 1, 0, 0, 4, 2, 0, 3, 2, 1, 5, 3, 0, 2, 3, 0, 0, 4, 0, 0, 5, 1, 0, 4, 5, 3, 0, 0, 0, 2, 0, 0, 5, 0, 0, 0, 0, 8, 3, 4, 2, 20, 2, 5, 4, 0, 0, 0, 2, 2, 3, 4, 1, 6, 6, 1, 1, 2, 14, 0, 8, 0, 0, 0, 0, 0, 0, 3, 6, 1, 0, 3, 1, 0, 1, 0, 2, 3, 0, 1, 5, 1, 0, 13, 2, 1, 0, 4, 3, 0, 0, 3, 0, 4, 1, 9, 9, 11, 1, 6, 2, 7, 0, 2, 0, 0, 3, 1, 0, 0, 0, 2, 1, 0, 6, 1, 13, 0, 0, 2, 5, 1, 1, 0, 10, 2, 11, 1, 12, 5, 1, 1, 0, 1, 1, 2, 4, 4, 3, 7, 1, 1, 3, 1, 5, 6, 0, 0, 4, 4, 2, 3, 1, 0, 1, 0, 2, 6, 1, 0, 0, 0, 2, 7, 1, 4, 1, 0, 1, 0, 7, 10, 0, 1, 1, 1, 2, 3, 0, 0, 0, 0, 0, 15, 0, 8, 1, 2, 0, 0, 1, 1, 1, 1, 0, 3, 2, 2, 3, 5, 7, 0, 1, 2, 1, 4, 2, 13, 4, 4, 0, 0, 0, 0, 0, 3, 2, 4, 3
];
function parseNumbers(numbersStr) {
    if (!numbersStr)
        return [];
    if (numbersStr.includes(',')) {
        return numbersStr.split(',').map(n => parseInt(n.trim())).filter(num => !isNaN(num));
    }
    else {
        return numbersStr.split('').map(n => parseInt(n)).filter(num => !isNaN(num));
    }
}
function calculateAllMissValues(results) {
    const periodsData = [];
    for (let i = 0; i < results.length; i++) {
        const currentResult = results[i];
        if (!currentResult)
            continue;
        const currentNumbers = parseNumbers(currentResult.numbers.join(''));
        const missValues = new Array(10).fill(0);
        for (let digit = 0; digit <= 9; digit++) {
            if (currentNumbers.includes(digit)) {
                missValues[digit] = 0;
            }
            else {
                let lastOccurrenceIndex = -1;
                for (let j = i - 1; j >= 0; j--) {
                    const checkResult = results[j];
                    if (!checkResult)
                        continue;
                    const checkNumbers = parseNumbers(checkResult.numbers.join(''));
                    if (checkNumbers.includes(digit)) {
                        lastOccurrenceIndex = j;
                        break;
                    }
                }
                if (lastOccurrenceIndex >= 0) {
                    missValues[digit] = i - lastOccurrenceIndex;
                }
                else {
                    missValues[digit] = i + 1;
                }
            }
        }
        periodsData.push({
            period: currentResult.period,
            numbers: currentNumbers,
            missValues: missValues
        });
    }
    return periodsData;
}
function buildNumber8OpenMissValueArray(periodsData) {
    const number8OpenMissValues = [];
    let isFirstOccurrence = true;
    for (let i = 0; i < periodsData.length; i++) {
        const currentPeriod = periodsData[i];
        if (!currentPeriod)
            continue;
        const uniqueNumbers = [...new Set(currentPeriod.numbers)];
        if (uniqueNumbers.includes(8)) {
            if (isFirstOccurrence) {
                number8OpenMissValues.push(0);
                isFirstOccurrence = false;
            }
            else {
                const prevPeriod = periodsData[i - 1];
                if (prevPeriod) {
                    const prevMissValue = prevPeriod.missValues[8];
                    if (prevMissValue !== undefined) {
                        number8OpenMissValues.push(prevMissValue);
                    }
                }
            }
        }
    }
    if (periodsData.length > 0) {
        const lastPeriod = periodsData[periodsData.length - 1];
        if (lastPeriod && lastPeriod.missValues && lastPeriod.missValues[8] !== undefined && lastPeriod.missValues[8] > 0) {
            const missValue = lastPeriod.missValues[8];
            if (missValue !== undefined) {
                number8OpenMissValues.push(missValue);
            }
        }
    }
    return number8OpenMissValues;
}
async function verifyNumber8MissValues() {
    try {
        console.log('🔄 初始化数据库连接...');
        await database_1.AppDataSource.initialize();
        console.log('✅ 数据库连接成功');
        const rawResults = await database_1.AppDataSource.query(`
      SELECT period, draw_time, numbers
      FROM lottery_results
      WHERE lottery_type_id = 4
      AND period >= '3305976'
      AND period <= '3307133'
      ORDER BY period ASC
    `);
        console.log(`📊 获取到 ${rawResults.length} 期PC28历史数据`);
        const results = rawResults.map((row) => ({
            period: row.period,
            drawTime: row.draw_time,
            numbers: parseNumbers(row.numbers)
        }));
        const periodsData = calculateAllMissValues(results);
        console.log(`📊 计算了 ${periodsData.length} 期的遗漏值`);
        const algorithmNumber8OpenMissValues = buildNumber8OpenMissValueArray(periodsData);
        console.log(`\n🎯 号码8开出时的上期遗漏值对比:`);
        console.log(`Excel数组长度: ${excelNumber8OpenMissValues.length}`);
        console.log(`算法数组长度: ${algorithmNumber8OpenMissValues.length}`);
        console.log(`\n📊 前30个开出号码8的期次对比:`);
        console.log(`位置\t期号\t\t开奖号码\tExcel\t算法\t匹配`);
        console.log(`${'='.repeat(70)}`);
        let matchCount = 0;
        let number8OpenIndex = 0;
        const compareCount = Math.min(30, excelNumber8OpenMissValues.length, algorithmNumber8OpenMissValues.length);
        for (let i = 0; i < periodsData.length && number8OpenIndex < compareCount; i++) {
            const period = periodsData[i];
            if (!period)
                continue;
            const uniqueNumbers = [...new Set(period.numbers)];
            if (uniqueNumbers.includes(8)) {
                const excelValue = excelNumber8OpenMissValues[number8OpenIndex];
                const algorithmValue = algorithmNumber8OpenMissValues[number8OpenIndex];
                if (excelValue !== undefined && algorithmValue !== undefined) {
                    const match = excelValue === algorithmValue;
                    if (match)
                        matchCount++;
                    const status = match ? '✅' : '❌';
                    console.log(`${(number8OpenIndex + 1).toString().padStart(2)}\t${period.period}\t[${period.numbers.join(',')}]\t\t${excelValue.toString().padStart(2)}\t${algorithmValue.toString().padStart(2)}\t${status}`);
                }
                number8OpenIndex++;
            }
        }
        console.log(`\n前30个值匹配率: ${matchCount}/${compareCount} = ${(matchCount / compareCount * 100).toFixed(1)}%`);
        let totalMatches = 0;
        const totalCompare = Math.min(excelNumber8OpenMissValues.length, algorithmNumber8OpenMissValues.length);
        for (let i = 0; i < totalCompare; i++) {
            if (excelNumber8OpenMissValues[i] === algorithmNumber8OpenMissValues[i]) {
                totalMatches++;
            }
        }
        console.log(`\n📊 全量对比统计:`);
        console.log(`总匹配数: ${totalMatches}/${totalCompare}`);
        console.log(`总匹配率: ${(totalMatches / totalCompare * 100).toFixed(1)}%`);
        if (totalMatches === totalCompare) {
            console.log(`\n🎉 完美匹配！号码8开出时的上期遗漏值算法完全正确！`);
        }
        else {
            console.log(`\n🔍 存在差异，显示前10个差异:`);
            console.log(`位置\t期号\t\t开奖号码\tExcel\t算法\t差值`);
            console.log(`${'='.repeat(70)}`);
            let diffCount = 0;
            let number8Index = 0;
            for (let i = 0; i < periodsData.length && diffCount < 10 && number8Index < totalCompare; i++) {
                const period = periodsData[i];
                if (!period)
                    continue;
                const uniqueNumbers = [...new Set(period.numbers)];
                if (uniqueNumbers.includes(8)) {
                    const excelValue = excelNumber8OpenMissValues[number8Index];
                    const algorithmValue = algorithmNumber8OpenMissValues[number8Index];
                    if (excelValue !== undefined && algorithmValue !== undefined && excelValue !== algorithmValue) {
                        const diff = excelValue - algorithmValue;
                        console.log(`${(number8Index + 1).toString().padStart(2)}\t${period.period}\t[${period.numbers.join(',')}]\t\t${excelValue.toString().padStart(2)}\t${algorithmValue.toString().padStart(2)}\t${diff > 0 ? '+' : ''}${diff}`);
                        diffCount++;
                    }
                    number8Index++;
                }
            }
        }
    }
    catch (error) {
        console.error('❌ 验证失败:', error);
    }
    finally {
        if (database_1.AppDataSource.isInitialized) {
            await database_1.AppDataSource.destroy();
            console.log('🔄 数据库连接已关闭');
        }
    }
}
if (require.main === module) {
    verifyNumber8MissValues().catch(console.error);
}
//# sourceMappingURL=verifyNumber8MissValues.js.map