{"version": 3, "file": "testCollectionWindow.js", "sourceRoot": "", "sources": ["../../src/scripts/testCollectionWindow.ts"], "names": [], "mappings": ";;;;;AA8IS,oDAAoB;AAAE,kEAA2B;AA9I1D,iDAAmD;AACnD,6EAA0E;AAC1E,oDAA4B;AAC5B,gDAAwB;AAGxB,gBAAM,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,eAAe,CAAC,EAAE,CAAC,CAAC;AAK/D,KAAK,UAAU,oBAAoB;IACjC,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAC9B,MAAM,wBAAa,CAAC,UAAU,EAAE,CAAC;QACjC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAGzB,MAAM,oBAAoB,GAAG,MAAM,wBAAa,CAAC,KAAK,CAAC;;;;;KAKtD,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,SAAS,oBAAoB,CAAC,MAAM,SAAS,CAAC,CAAC;QAC3D,oBAAoB,CAAC,OAAO,CAAC,CAAC,OAAY,EAAE,EAAE;YAC5C,OAAO,CAAC,GAAG,CAAC,QAAQ,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,WAAW,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;QACnF,CAAC,CAAC,CAAC;QAGH,MAAM,iBAAiB,GAAG,6CAAqB,CAAC,WAAW,EAAE,CAAC;QAG9D,KAAK,MAAM,OAAO,IAAI,oBAAoB,EAAE,CAAC;YAC3C,OAAO,CAAC,GAAG,CAAC,WAAW,OAAO,CAAC,IAAI,aAAa,CAAC,CAAC;YAElD,MAAM,QAAQ,GAAG,OAAO,CAAC,SAAS,CAAC;YACnC,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAGlE,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;YACzB,MAAM,aAAa,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,KAAK,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;YAGhH,MAAM,cAAc,GAAG,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;YAE1E,OAAO,CAAC,GAAG,CAAC,iBAAiB,aAAa,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;YAC/D,OAAO,CAAC,GAAG,CAAC,gBAAgB,cAAc,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;YAC/D,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;YAGlC,MAAM,SAAS,GAAG;gBAChB,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC/B,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,EAAE;gBAC3B,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,EAAE,EAAE;gBAC/B,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,EAAE,EAAE;gBAC/B,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,EAAE,EAAE;aAChC,CAAC;YAEF,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;gBACjC,MAAM,YAAY,GAAG,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,GAAG,QAAQ,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;gBACrF,MAAM,aAAa,GAAG,YAAY,IAAI,aAAa,IAAI,YAAY,IAAI,cAAc,CAAC;gBAEtF,OAAO,CAAC,GAAG,CAAC,MAAM,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,QAAQ,CAAC,IAAI,KAAK,YAAY,CAAC,kBAAkB,EAAE,MAAM,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;YAC7I,CAAC;QACH,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAC9B,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACxB,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;QACxC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IAEhC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;IAClC,CAAC;YAAS,CAAC;QACT,IAAI,wBAAa,CAAC,aAAa,EAAE,CAAC;YAChC,MAAM,wBAAa,CAAC,OAAO,EAAE,CAAC;YAC9B,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAC7B,CAAC;IACH,CAAC;AACH,CAAC;AAKD,KAAK,UAAU,2BAA2B;IACxC,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;QAEnC,MAAM,wBAAa,CAAC,UAAU,EAAE,CAAC;QAEjC,MAAM,oBAAoB,GAAG,MAAM,wBAAa,CAAC,KAAK,CAAC;;;;;KAKtD,CAAC,CAAC;QAEH,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,YAAY,GAAG,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;QAEhD,KAAK,MAAM,OAAO,IAAI,oBAAoB,EAAE,CAAC;YAC3C,MAAM,QAAQ,GAAG,OAAO,CAAC,SAAS,CAAC;YACnC,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAElE,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;YACzB,MAAM,aAAa,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,KAAK,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;YAChH,MAAM,cAAc,GAAG,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;YAE1E,MAAM,QAAQ,GAAG,GAAG,IAAI,aAAa,IAAI,GAAG,IAAI,cAAc,CAAC;YAE/D,OAAO,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC1F,OAAO,CAAC,GAAG,CAAC,YAAY,aAAa,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC;YAC9D,OAAO,CAAC,GAAG,CAAC,YAAY,cAAc,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC;QACjE,CAAC;IAEH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;IAClC,CAAC;YAAS,CAAC;QACT,IAAI,wBAAa,CAAC,aAAa,EAAE,CAAC;YAChC,MAAM,wBAAa,CAAC,OAAO,EAAE,CAAC;QAChC,CAAC;IACH,CAAC;AACH,CAAC;AAGD,KAAK,UAAU,IAAI;IACjB,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAEnC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE,CAAC;QAC7C,MAAM,2BAA2B,EAAE,CAAC;IACtC,CAAC;SAAM,CAAC;QACN,MAAM,oBAAoB,EAAE,CAAC;IAC/B,CAAC;AACH,CAAC;AAGD,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,IAAI,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC9B,CAAC"}