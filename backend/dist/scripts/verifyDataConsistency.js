"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyDataConsistency = verifyDataConsistency;
const database_1 = require("../config/database");
const dotenv_1 = __importDefault(require("dotenv"));
const path_1 = __importDefault(require("path"));
dotenv_1.default.config({ path: path_1.default.join(__dirname, '../../../.env') });
async function verifyDataConsistency() {
    try {
        console.log('🔄 初始化数据库连接...');
        await database_1.AppDataSource.initialize();
        console.log('✅ 数据库连接成功');
        const rawResults = await database_1.AppDataSource.query(`
      SELECT period, numbers
      FROM lottery_results
      WHERE lottery_type_id = 4
      AND period >= '3305976'
      AND period <= '3307133'
      ORDER BY period ASC
    `);
        console.log(`📊 数据库中获取到 ${rawResults.length} 期数据`);
        if (rawResults.length > 0) {
            const firstPeriod = rawResults[0].period;
            const lastPeriod = rawResults[rawResults.length - 1].period;
            console.log(`📋 期号范围: ${firstPeriod} - ${lastPeriod}`);
        }
        console.log(`\n🎯 前20期数据对比:`);
        console.log(`期号\t\t数据库号码\tExcel格式`);
        console.log(`${'='.repeat(50)}`);
        for (let i = 0; i < Math.min(20, rawResults.length); i++) {
            const row = rawResults[i];
            const period = row.period;
            const dbNumbers = row.numbers;
            const excelFormat = dbNumbers.replace(/,/g, '');
            console.log(`${period}\t${dbNumbers}\t\t${excelFormat}`);
        }
        console.log(`\n🎯 最后20期数据对比:`);
        console.log(`期号\t\t数据库号码\tExcel格式`);
        console.log(`${'='.repeat(50)}`);
        const startIndex = Math.max(0, rawResults.length - 20);
        for (let i = startIndex; i < rawResults.length; i++) {
            const row = rawResults[i];
            const period = row.period;
            const dbNumbers = row.numbers;
            const excelFormat = dbNumbers.replace(/,/g, '');
            console.log(`${period}\t${dbNumbers}\t\t${excelFormat}`);
        }
        const checkPeriods = ['3305976', '3305979', '3305980', '3307131', '3307132', '3307133'];
        console.log(`\n🔍 检查特定期号数据:`);
        for (const checkPeriod of checkPeriods) {
            const found = rawResults.find((row) => row.period === checkPeriod);
            if (found) {
                const excelFormat = found.numbers.replace(/,/g, '');
                console.log(`期号${checkPeriod}: ${found.numbers} -> ${excelFormat}`);
            }
            else {
                console.log(`❌ 期号${checkPeriod}: 未找到数据`);
            }
        }
        console.log(`\n📊 数据完整性检查:`);
        const expectedCount = 3307133 - 3305976 + 1;
        console.log(`期号范围应有期数: ${expectedCount}`);
        console.log(`实际获取期数: ${rawResults.length}`);
        console.log(`数据完整性: ${rawResults.length === expectedCount ? '✅ 完整' : '❌ 不完整'}`);
        if (rawResults.length !== expectedCount) {
            const existingPeriods = new Set(rawResults.map((row) => parseInt(row.period)));
            const missingPeriods = [];
            for (let period = 3305976; period <= 3307133; period++) {
                if (!existingPeriods.has(period)) {
                    missingPeriods.push(period);
                }
            }
            console.log(`❌ 缺失期号数量: ${missingPeriods.length}`);
            if (missingPeriods.length <= 20) {
                console.log(`缺失期号: ${missingPeriods.join(', ')}`);
            }
            else {
                console.log(`缺失期号(前10个): ${missingPeriods.slice(0, 10).join(', ')}...`);
            }
        }
        console.log(`\n🔍 数据格式验证:`);
        let formatErrors = 0;
        for (let i = 0; i < Math.min(100, rawResults.length); i++) {
            const row = rawResults[i];
            const numbers = row.numbers;
            const parts = numbers.split(',');
            if (parts.length !== 3) {
                console.log(`❌ 期号${row.period}: 格式错误 "${numbers}"`);
                formatErrors++;
            }
            else {
                for (const part of parts) {
                    const num = parseInt(part.trim());
                    if (isNaN(num) || num < 0 || num > 9) {
                        console.log(`❌ 期号${row.period}: 数字错误 "${numbers}"`);
                        formatErrors++;
                        break;
                    }
                }
            }
        }
        console.log(`格式错误数量: ${formatErrors}`);
        console.log(`格式验证: ${formatErrors === 0 ? '✅ 正确' : '❌ 有错误'}`);
    }
    catch (error) {
        console.error('❌ 验证失败:', error);
    }
    finally {
        if (database_1.AppDataSource.isInitialized) {
            await database_1.AppDataSource.destroy();
            console.log('🔄 数据库连接已关闭');
        }
    }
}
if (require.main === module) {
    verifyDataConsistency().catch(console.error);
}
//# sourceMappingURL=verifyDataConsistency.js.map