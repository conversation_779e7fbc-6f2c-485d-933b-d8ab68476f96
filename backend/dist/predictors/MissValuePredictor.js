"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MissValuePredictor = void 0;
const database_1 = require("../config/database");
const LotteryResult_1 = require("../models/LotteryResult");
class MissValuePredictor {
    constructor(lotteryTypeId, positions) {
        this.lotteryTypeId = lotteryTypeId;
        this.positions = positions;
    }
    async generatePrediction() {
        console.log(`🎯 开始为彩种 ${this.lotteryTypeId} 生成胆码预测...`);
        try {
            const recentResults = await this.getRecentResults(1000);
            if (recentResults.length < 100) {
                console.warn(`⚠️  历史数据不足（${recentResults.length}期），无法生成可靠预测`);
                return { prediction: '' };
            }
            console.log(`📊 获取到 ${recentResults.length} 期历史数据`);
            const periodsData = this.calculateAllMissValues(recentResults);
            const missValueArrays = this.generateMissValueArrays(periodsData);
            const currentMissValues = this.calculateCurrentMissValues(recentResults);
            const counts = this.countCurrentMissInArrays(missValueArrays, currentMissValues);
            const { firstGroup, secondGroup } = this.generateDanmaGroups(currentMissValues, counts);
            const combined = [...firstGroup, ...secondGroup].sort((a, b) => a - b);
            console.log(`🎯 第一组胆码（遗漏为0）:`, firstGroup);
            console.log(`🎯 第二组胆码（遗漏非0）:`, secondGroup);
            console.log(`🎯 合并胆码:`, combined);
            return {
                prediction: combined.join(',')
            };
        }
        catch (error) {
            console.error(`❌ 生成预测失败:`, error);
            return { prediction: '' };
        }
    }
    async getRecentResults(limit) {
        const results = await database_1.AppDataSource.query(`
      SELECT id, lottery_type_id, period, draw_time, numbers
      FROM lottery_results
      WHERE lottery_type_id = ?
      ORDER BY draw_time ASC
      LIMIT ?
    `, [this.lotteryTypeId, limit]);
        return results.map((row) => {
            const result = new LotteryResult_1.LotteryResult();
            result.id = row.id;
            result.lotteryTypeId = row.lottery_type_id;
            result.period = row.period;
            result.drawTime = row.draw_time;
            result.numbers = row.numbers;
            return result;
        });
    }
    calculateAllMissValues(results) {
        const periodsData = [];
        for (let i = 0; i < results.length; i++) {
            const currentResult = results[i];
            const missValues = new Array(10).fill(0);
            for (let digit = 0; digit <= 9; digit++) {
                let missCount = 0;
                for (let j = i - 1; j >= 0; j--) {
                    const prevResult = results[j];
                    if (!prevResult)
                        continue;
                    const prevNumbers = this.parseNumbers(prevResult.numbers);
                    if (prevNumbers.includes(digit)) {
                        break;
                    }
                    missCount++;
                }
                missValues[digit] = missCount;
            }
            if (currentResult) {
                periodsData.push({
                    period: currentResult.period,
                    numbers: this.parseNumbers(currentResult.numbers),
                    missValues: missValues
                });
            }
        }
        return periodsData;
    }
    generateMissValueArrays(periodsData) {
        const missValueArrays = new Map();
        for (let digit = 0; digit <= 9; digit++) {
            missValueArrays.set(digit, []);
        }
        for (let i = 1; i < periodsData.length; i++) {
            const currentPeriod = periodsData[i];
            const prevPeriod = periodsData[i - 1];
            if (!currentPeriod || !prevPeriod)
                continue;
            for (const digit of currentPeriod.numbers) {
                const prevMissValue = prevPeriod.missValues[digit];
                if (prevMissValue !== undefined) {
                    const missArray = missValueArrays.get(digit);
                    missArray.push(prevMissValue);
                }
            }
        }
        for (let digit = 0; digit <= 9; digit++) {
            const array = missValueArrays.get(digit);
            console.log(`号码${digit}的遗漏值数组: [${array.join(',')}] (${array.length}个)`);
        }
        return missValueArrays;
    }
    calculateCurrentMissValues(results) {
        const currentMissValues = new Array(10).fill(0);
        for (let digit = 0; digit <= 9; digit++) {
            let missCount = 0;
            for (let i = results.length - 1; i >= 0; i--) {
                const result = results[i];
                if (!result)
                    continue;
                const numbers = this.parseNumbers(result.numbers);
                if (numbers.includes(digit)) {
                    break;
                }
                missCount++;
            }
            currentMissValues[digit] = missCount;
        }
        console.log(`当前遗漏值:`, currentMissValues.map((miss, digit) => `${digit}:${miss}`).join(', '));
        return currentMissValues;
    }
    countCurrentMissInArrays(missValueArrays, currentMissValues) {
        const counts = new Array(10).fill(0);
        for (let digit = 0; digit <= 9; digit++) {
            const currentMiss = currentMissValues[digit];
            const missArray = missValueArrays.get(digit);
            const count = missArray.filter(value => value === currentMiss).length;
            counts[digit] = count;
            console.log(`号码${digit}当前遗漏${currentMiss}，遗漏值数组中有${currentMiss}，${count}个`);
        }
        return counts;
    }
    generateDanmaGroups(currentMissValues, counts) {
        const zeroMissDigits = [];
        for (let digit = 0; digit <= 9; digit++) {
            if (currentMissValues[digit] === 0) {
                zeroMissDigits.push(digit);
            }
        }
        let firstGroup = [];
        if (zeroMissDigits.length > 0) {
            let maxCount = -1;
            for (const digit of zeroMissDigits) {
                const count = counts[digit];
                if (count !== undefined && count > maxCount) {
                    maxCount = count;
                }
            }
            for (const digit of zeroMissDigits) {
                const count = counts[digit];
                if (count !== undefined && count === maxCount) {
                    firstGroup.push(digit);
                }
            }
        }
        const nonZeroMissDigits = [];
        for (let digit = 0; digit <= 9; digit++) {
            if (currentMissValues[digit] !== 0) {
                nonZeroMissDigits.push(digit);
            }
        }
        let secondGroup = [];
        if (nonZeroMissDigits.length > 0) {
            let maxCount = -1;
            for (const digit of nonZeroMissDigits) {
                const count = counts[digit];
                if (count !== undefined && count > maxCount) {
                    maxCount = count;
                }
            }
            for (const digit of nonZeroMissDigits) {
                const count = counts[digit];
                if (count !== undefined && count === maxCount) {
                    secondGroup.push(digit);
                }
            }
        }
        return { firstGroup, secondGroup };
    }
    parseNumbers(numbersStr) {
        if (numbersStr.includes(',')) {
            return numbersStr.split(',').map(n => parseInt(n.trim()));
        }
        else {
            return numbersStr.split('').map(n => parseInt(n));
        }
    }
}
exports.MissValuePredictor = MissValuePredictor;
//# sourceMappingURL=MissValuePredictor.js.map