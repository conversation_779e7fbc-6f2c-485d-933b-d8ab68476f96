import { AppDataSource } from '../config/database';
import dotenv from 'dotenv';
import path from 'path';

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '../../../.env') });

// 您提供的Excel中号码0的1152个遗漏值
const excelMissValues = [
  0,1,2,0,0,1,2,0,1,2,3,4,5,0,1,2,3,4,5,0,1,2,3,4,5,6,7,8,0,1,2,0,1,0,1,2,3,4,5,0,1,0,1,0,1,2,3,0,1,2,3,0,1,2,3,4,5,0,0,1,2,3,4,5,0,0,1,2,3,4,0,0,0,1,0,1,2,3,4,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,0,1,2,3,4,5,6,0,1,0,1,2,0,1,0,0,1,2,3,0,0,1,0,0,1,2,0,1,2,3,0,0,1,2,3,4,5,0,1,2,3,4,5,0,1,2,3,4,5,6,7,8,9,0,1,2,3,4,5,6,0,0,1,2,3,0,1,2,3,0,1,2,0,1,2,3,4,5,0,0,0,1,2,3,4,0,1,2,3,4,5,6,7,0,1,2,3,4,5,0,1,2,3,4,5,6,7,8,0,1,2,3,4,5,0,1,2,0,0,1,2,3,4,5,0,1,0,1,2,0,1,2,0,1,2,0,1,0,1,0,1,0,1,2,3,4,0,1,0,1,2,0,0,1,0,1,2,3,4,5,6,0,1,2,0,1,2,3,4,5,6,7,0,1,2,3,4,5,6,7,8,0,1,2,3,4,5,6,0,1,0,1,2,0,1,0,1,2,3,4,5,6,7,0,1,2,3,0,1,2,3,4,5,6,0,0,0,0,0,1,2,3,0,1,2,3,4,0,0,0,1,2,3,4,5,6,0,0,1,0,1,2,3,0,0,1,2,3,4,0,0,1,2,0,1,2,0,1,0,0,1,2,0,0,1,2,0,1,2,3,4,5,6,0,0,1,0,1,2,3,0,0,1,0,0,1,2,0,0,1,2,3,4,5,6,7,8,9,0,1,2,3,4,0,0,1,0,1,2,3,0,1,2,3,4,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,0,1,2,3,4,5,6,7,8,9,10,11,0,1,2,3,4,0,1,2,0,1,2,0,1,2,0,0,1,2,0,1,2,3,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,0,1,2,3,4,5,6,7,8,9,0,0,1,2,3,4,0,1,2,3,4,5,0,1,2,0,1,2,3,4,5,6,0,1,0,1,2,3,4,5,6,7,8,0,1,2,3,4,5,6,0,1,2,3,4,0,0,0,1,0,1,2,3,4,5,6,0,0,0,1,0,0,1,0,0,1,2,3,4,5,6,7,8,0,1,2,3,4,5,6,7,8,9,10,11,0,1,2,0,0,1,2,0,1,0,1,0,1,0,1,2,0,1,2,0,1,2,3,4,5,6,7,8,0,0,0,1,2,3,4,5,0,1,2,0,1,0,1,2,3,0,1,2,3,4,5,6,0,1,2,3,4,0,0,1,2,0,1,2,0,1,2,3,4,0,1,2,0,0,0,1,2,0,0,1,2,0,1,0,0,1,2,0,1,2,3,4,5,6,7,0,1,0,1,2,3,4,5,6,7,0,1,2,3,4,5,6,7,0,1,2,3,4,5,6,0,1,2,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,0,1,0,0,1,0,0,1,0,1,2,3,0,1,2,3,0,1,0,0,1,0,1,0,1,2,0,0,1,2,3,4,5,0,0,1,2,3,4,5,6,7,8,0,1,2,3,4,5,6,7,8,0,1,2,0,1,2,3,0,0,1,2,3,0,0,1,2,3,4,5,6,7,8,9,0,1,0,0,1,2,3,4,5,6,7,8,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,0,1,2,3,4,5,0,1,2,3,0,1,0,1,0,1,2,0,1,0,1,0,1,2,3,4,5,0,0,1,2,0,1,2,3,4,5,0,0,1,0,1,2,3,4,5,6,7,8,9,0,1,0,1,2,0,0,1,2,0,1,2,3,4,5,6,7,8,9,10,0,0,1,2,0,1,2,3,4,5,0,1,2,3,4,5,0,0,1,2,3,4,5,6,0,1,2,3,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,0,1,2,3,4,5,0,1,2,0,1,2,0,0,1,0,1,0,0,1,2,3,4,5,0,1,2,3,0,0,0,1,2,3,0,1,2,0,1,0,1,2,3,4,5,0,1,2,3,4,5,0,1,0,0,1,2,3,4,5,0,0,1,2,3,4,5,6,7,8,9,0,1,0,0,1,2,3,4,5,6,7,8,0,1,2,3,0,1,2,0,1,2,3,4,0,0,1,2,3,4,5,6,7,8,9,10,11,12,0,1,0,0,1,2,0,0,0,1,0,1,2,0,1,2,0,1,2,3,4,5,6,0,1,2,3,4,5,6,7,8,9,0,1,2,3,4,5,6,0,1,2,3,4,5,6,7,0,1,2,3,4,5,6,0,1,2,0,1,0,1,0,1,2,3,4,5,0,1,0,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,0,1,0,1,2,0,0,1,0,1,0,0,1,0,0,0
];

interface LotteryResult {
  period: string;
  drawTime: string;
  numbers: number[];
}

interface PeriodMissData {
  period: string;
  numbers: number[];
  missValues: number[];
}

/**
 * 解析号码字符串
 */
function parseNumbers(numbersStr: string): number[] {
  if (!numbersStr) return [];
  
  if (numbersStr.includes(',')) {
    return numbersStr.split(',').map(n => parseInt(n.trim())).filter(num => !isNaN(num));
  } else {
    return numbersStr.split('').map(n => parseInt(n)).filter(num => !isNaN(num));
  }
}

/**
 * 计算所有期数的遗漏值（修正版 - 与Excel逻辑一致）
 */
function calculateAllMissValues(results: LotteryResult[]): PeriodMissData[] {
  const periodsData: PeriodMissData[] = [];

  for (let i = 0; i < results.length; i++) {
    const currentResult = results[i];
    if (!currentResult) continue;

    const currentNumbers = parseNumbers(currentResult.numbers.join(''));
    const missValues = new Array(10).fill(0);

    // 计算每个号码(0-9)在当前期的遗漏值（Excel逻辑）
    for (let digit = 0; digit <= 9; digit++) {
      // 如果当前期开出了该号码，遗漏值为0
      if (currentNumbers.includes(digit)) {
        missValues[digit] = 0;
      } else {
        // 如果当前期没有开出该号码，计算距离上次开出的期数
        let lastOccurrenceIndex = -1;

        // 从上一期往前查找该数字上次出现的位置
        for (let j = i - 1; j >= 0; j--) {
          const checkResult = results[j];
          if (!checkResult) continue;

          const checkNumbers = parseNumbers(checkResult.numbers.join(''));

          if (checkNumbers.includes(digit)) {
            lastOccurrenceIndex = j;
            break; // 找到该数字，停止查找
          }
        }

        // 遗漏值 = 当前期位置 - 上次开出该号码的期位置
        if (lastOccurrenceIndex >= 0) {
          missValues[digit] = i - lastOccurrenceIndex;
        } else {
          // 如果从第1期到当前期都没出现过，遗漏值为当前期的位置（从1开始计数）
          missValues[digit] = i + 1;
        }
      }
    }

    periodsData.push({
      period: currentResult.period,
      numbers: currentNumbers,
      missValues: missValues
    });
  }

  return periodsData;
}

/**
 * 对比遗漏值
 */
async function compareMissValues() {
  try {
    console.log('🔄 初始化数据库连接...');
    await AppDataSource.initialize();
    console.log('✅ 数据库连接成功');

    // 获取数据库中的1152期数据
    const rawResults = await AppDataSource.query(`
      SELECT period, draw_time, numbers
      FROM lottery_results
      WHERE lottery_type_id = 4
      AND period >= '3305976'
      AND period <= '3307133'
      ORDER BY period ASC
    `);

    console.log(`📊 获取到 ${rawResults.length} 期PC28历史数据`);

    // 转换数据格式
    const results: LotteryResult[] = rawResults.map((row: any) => ({
      period: row.period,
      drawTime: row.draw_time,
      numbers: parseNumbers(row.numbers)
    }));

    // 计算遗漏值
    const periodsData = calculateAllMissValues(results);
    console.log(`📊 计算了 ${periodsData.length} 期的遗漏值`);

    // 提取算法计算的号码0遗漏值
    const algorithmMissValues = periodsData.map(period => period.missValues[0]);

    console.log(`\n🎯 遗漏值对比分析:`);
    console.log(`Excel遗漏值数量: ${excelMissValues.length}`);
    console.log(`算法遗漏值数量: ${algorithmMissValues.length}`);

    // 对比前50个遗漏值
    console.log(`\n📊 前50期遗漏值对比:`);
    console.log(`位置\t期号\t\tExcel\t算法\t匹配`);
    console.log(`${'='.repeat(60)}`);

    let matchCount = 0;
    const compareCount = Math.min(50, excelMissValues.length, algorithmMissValues.length);
    
    for (let i = 0; i < compareCount; i++) {
      const excelValue = excelMissValues[i];
      const algorithmValue = algorithmMissValues[i];

      if (excelValue === undefined || algorithmValue === undefined) continue;

      const match = excelValue === algorithmValue;

      if (match) matchCount++;

      const status = match ? '✅' : '❌';
      const period = periodsData[i]?.period || 'N/A';

      console.log(`${(i+1).toString().padStart(3)}\t${period}\t${excelValue.toString().padStart(2)}\t${algorithmValue.toString().padStart(2)}\t${status}`);
    }
    
    console.log(`\n前50期匹配率: ${matchCount}/${compareCount} = ${(matchCount/compareCount*100).toFixed(1)}%`);

    // 全量对比统计
    let totalMatches = 0;
    const totalCompare = Math.min(excelMissValues.length, algorithmMissValues.length);
    
    for (let i = 0; i < totalCompare; i++) {
      if (excelMissValues[i] === algorithmMissValues[i]) {
        totalMatches++;
      }
    }
    
    console.log(`\n📊 全量对比统计:`);
    console.log(`总匹配数: ${totalMatches}/${totalCompare}`);
    console.log(`总匹配率: ${(totalMatches/totalCompare*100).toFixed(1)}%`);

    // 找出差异模式
    if (totalMatches < totalCompare) {
      console.log(`\n🔍 差异分析:`);
      const differences: number[] = [];
      
      for (let i = 0; i < Math.min(100, totalCompare); i++) {
        if (excelMissValues[i] !== algorithmMissValues[i]) {
          differences.push(i);
        }
      }
      
      console.log(`前100期中的差异位置: ${differences.slice(0, 10).join(', ')}${differences.length > 10 ? '...' : ''}`);
      
      // 显示前几个差异的详细信息
      console.log(`\n📋 前10个差异详情:`);
      console.log(`位置\t期号\t\t开奖号码\tExcel\t算法\t差值`);
      console.log(`${'='.repeat(70)}`);
      
      for (let i = 0; i < Math.min(10, differences.length); i++) {
        const pos = differences[i];
        if (pos === undefined) continue;

        const period = periodsData[pos];
        if (period) {
          const excelValue = excelMissValues[pos];
          const algorithmValue = algorithmMissValues[pos];

          if (excelValue !== undefined && algorithmValue !== undefined) {
            const diff = excelValue - algorithmValue;

            console.log(`${(pos+1).toString().padStart(3)}\t${period.period}\t[${period.numbers.join(',')}]\t\t${excelValue.toString().padStart(2)}\t${algorithmValue.toString().padStart(2)}\t${diff > 0 ? '+' : ''}${diff}`);
          }
        }
      }
    }

  } catch (error) {
    console.error('❌ 对比失败:', error);
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('🔄 数据库连接已关闭');
    }
  }
}

// 运行对比
if (require.main === module) {
  compareMissValues().catch(console.error);
}

export { compareMissValues };
