import { AppDataSource } from '../config/database';
import dotenv from 'dotenv';
import path from 'path';

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '../../../.env') });

// 您重新提供的号码5开出时的上期遗漏值数组（331个元素）
const excelNumber5OpenMissValues = [
  0,3,0,0,5,1,3,4,1,0,1,1,0,2,2,3,0,1,1,7,0,0,4,4,1,0,13,14,3,2,0,0,6,1,7,6,5,0,1,0,4,5,1,3,5,5,3,0,2,5,0,2,4,8,1,1,4,2,11,1,3,1,0,0,1,0,10,0,0,3,6,3,2,3,2,5,0,0,3,0,1,0,3,0,2,9,2,0,4,2,2,3,4,4,1,5,3,5,7,1,4,5,5,0,3,0,4,0,0,5,1,3,3,0,1,0,11,3,8,0,1,1,1,0,5,1,2,0,1,5,3,2,1,0,4,1,4,1,0,3,2,0,0,1,1,6,0,7,3,1,7,17,4,5,4,1,0,0,3,2,0,8,0,0,1,3,1,2,2,5,6,0,0,3,2,4,3,3,4,2,2,1,2,3,1,4,0,1,10,8,1,5,2,2,1,0,2,0,6,1,0,7,1,2,1,1,0,1,0,6,0,2,0,0,0,0,1,4,1,3,0,0,1,0,7,0,5,3,1,0,0,3,5,1,0,0,10,1,1,0,2,2,4,0,4,7,1,1,5,4,1,1,3,3,3,0,0,0,6,0,2,1,11,5,4,0,2,1,5,1,6,0,0,14,8,5,0,13,0,0,4,0,3,0,0,0,5,2,2,0,1,1,0,0,0,1,4,2,0,3,8,3,6,3,4,0,0,0,0,2,3,0,1,5,0,4,5,2,1,5,0,2,2,4,2,1,2,0,2,2,6
];

interface LotteryResult {
  period: string;
  drawTime: string;
  numbers: number[];
}

interface PeriodMissData {
  period: string;
  numbers: number[];
  missValues: number[];
}

/**
 * 解析号码字符串
 */
function parseNumbers(numbersStr: string): number[] {
  if (!numbersStr) return [];
  
  if (numbersStr.includes(',')) {
    return numbersStr.split(',').map(n => parseInt(n.trim())).filter(num => !isNaN(num));
  } else {
    return numbersStr.split('').map(n => parseInt(n)).filter(num => !isNaN(num));
  }
}

/**
 * 计算所有期数的遗漏值（Excel逻辑）
 */
function calculateAllMissValues(results: LotteryResult[]): PeriodMissData[] {
  const periodsData: PeriodMissData[] = [];

  for (let i = 0; i < results.length; i++) {
    const currentResult = results[i];
    if (!currentResult) continue;
    
    const currentNumbers = parseNumbers(currentResult.numbers.join(''));
    const missValues = new Array(10).fill(0);

    // 计算每个号码(0-9)在当前期的遗漏值（Excel逻辑）
    for (let digit = 0; digit <= 9; digit++) {
      // 如果当前期开出了该号码，遗漏值为0
      if (currentNumbers.includes(digit)) {
        missValues[digit] = 0;
      } else {
        // 如果当前期没有开出该号码，计算距离上次开出的期数
        let lastOccurrenceIndex = -1;

        // 从上一期往前查找该数字上次出现的位置
        for (let j = i - 1; j >= 0; j--) {
          const checkResult = results[j];
          if (!checkResult) continue;

          const checkNumbers = parseNumbers(checkResult.numbers.join(''));

          if (checkNumbers.includes(digit)) {
            lastOccurrenceIndex = j;
            break; // 找到该数字，停止查找
          }
        }

        // 遗漏值 = 当前期位置 - 上次开出该号码的期位置
        if (lastOccurrenceIndex >= 0) {
          missValues[digit] = i - lastOccurrenceIndex;
        } else {
          // 如果从第1期到当前期都没出现过，遗漏值为当前期的位置（从1开始计数）
          missValues[digit] = i + 1;
        }
      }
    }

    periodsData.push({
      period: currentResult.period,
      numbers: currentNumbers,
      missValues: missValues
    });
  }

  return periodsData;
}

/**
 * 验证号码5的遗漏值
 */
async function verifyNumber5MissValues() {
  try {
    console.log('🔄 初始化数据库连接...');
    await AppDataSource.initialize();
    console.log('✅ 数据库连接成功');

    // 获取数据库中的1152期数据
    const rawResults = await AppDataSource.query(`
      SELECT period, draw_time, numbers
      FROM lottery_results
      WHERE lottery_type_id = 4
      AND period >= '3305976'
      AND period <= '3307133'
      ORDER BY period ASC
    `);

    console.log(`📊 获取到 ${rawResults.length} 期PC28历史数据`);

    // 转换数据格式
    const results: LotteryResult[] = rawResults.map((row: any) => ({
      period: row.period,
      drawTime: row.draw_time,
      numbers: parseNumbers(row.numbers)
    }));

    // 计算遗漏值
    const periodsData = calculateAllMissValues(results);
    console.log(`📊 计算了 ${periodsData.length} 期的遗漏值`);

    // 构建号码5开出时的上期遗漏值数组
    const algorithmNumber5OpenMissValues: number[] = [];

    for (let i = 0; i < periodsData.length; i++) {
      const currentPeriod = periodsData[i];
      if (!currentPeriod) continue;

      // 检查当前期是否开出号码5（去重处理）
      const uniqueNumbers = [...new Set(currentPeriod.numbers)];
      if (uniqueNumbers.includes(5)) {
        if (i === 0) {
          // 第一期：默认为0
          algorithmNumber5OpenMissValues.push(0);
        } else {
          // 其他期：记录上期号码5的遗漏值
          const prevPeriod = periodsData[i - 1];
          if (prevPeriod) {
            const prevMissValue = prevPeriod.missValues[5];
            if (prevMissValue !== undefined) {
              algorithmNumber5OpenMissValues.push(prevMissValue);
            }
          }
        }
      }
    }

    // 添加最后一期的处理：如果最新期号码5的遗漏值大于0，默认下期开出5
    if (periodsData.length > 0) {
      const lastPeriod = periodsData[periodsData.length - 1];
      if (lastPeriod && lastPeriod.missValues && lastPeriod.missValues[5] !== undefined && lastPeriod.missValues[5] > 0) {
        // 默认下期开出号码5，记录当前期号码5的遗漏值
        const missValue = lastPeriod.missValues[5];
        if (missValue !== undefined) {
          algorithmNumber5OpenMissValues.push(missValue);
        }
      }
    }

    console.log(`\n🎯 号码5开出时的上期遗漏值对比分析:`);
    console.log(`Excel数组长度: ${excelNumber5OpenMissValues.length}`);
    console.log(`算法数组长度: ${algorithmNumber5OpenMissValues.length}`);

    // 统计Excel数组中6的出现次数
    const excelSixCount = excelNumber5OpenMissValues.filter(val => val === 6).length;
    console.log(`Excel数组中6的出现次数: ${excelSixCount}`);

    // 统计算法数组中6的出现次数
    const algorithmSixCount = algorithmNumber5OpenMissValues.filter(val => val === 6).length;
    console.log(`算法数组中6的出现次数: ${algorithmSixCount}`);

    // 获取最新期号码5的遗漏值
    const latestPeriod = periodsData[periodsData.length - 1];
    if (latestPeriod) {
      console.log(`最新期(${latestPeriod.period})号码5的遗漏值: ${latestPeriod.missValues[5]}`);
    }

    // 对比前30个开出号码5的期次
    console.log(`\n📊 前30个开出号码5的期次对比:`);
    console.log(`位置\t期号\t\t开奖号码\tExcel\t算法\t匹配`);
    console.log(`${'='.repeat(70)}`);

    let matchCount = 0;
    let number5OpenIndex = 0;
    const compareCount = Math.min(30, excelNumber5OpenMissValues.length, algorithmNumber5OpenMissValues.length);

    for (let i = 0; i < periodsData.length && number5OpenIndex < compareCount; i++) {
      const period = periodsData[i];
      if (!period) continue;

      // 检查当前期是否开出号码5
      const uniqueNumbers = [...new Set(period.numbers)];
      if (uniqueNumbers.includes(5)) {
        const excelValue = excelNumber5OpenMissValues[number5OpenIndex];
        const algorithmValue = algorithmNumber5OpenMissValues[number5OpenIndex];

        if (excelValue !== undefined && algorithmValue !== undefined) {
          const match = excelValue === algorithmValue;
          if (match) matchCount++;

          const status = match ? '✅' : '❌';
          console.log(`${(number5OpenIndex + 1).toString().padStart(2)}\t${period.period}\t[${period.numbers.join(',')}]\t\t${excelValue.toString().padStart(2)}\t${algorithmValue.toString().padStart(2)}\t${status}`);
        }

        number5OpenIndex++;
      }
    }

    console.log(`\n前30个值匹配率: ${matchCount}/${compareCount} = ${(matchCount/compareCount*100).toFixed(1)}%`);

    // 全量对比统计
    let totalMatches = 0;
    const totalCompare = Math.min(excelNumber5OpenMissValues.length, algorithmNumber5OpenMissValues.length);

    for (let i = 0; i < totalCompare; i++) {
      if (excelNumber5OpenMissValues[i] === algorithmNumber5OpenMissValues[i]) {
        totalMatches++;
      }
    }
    
    console.log(`\n📊 全量对比统计:`);
    console.log(`总匹配数: ${totalMatches}/${totalCompare}`);
    console.log(`总匹配率: ${(totalMatches/totalCompare*100).toFixed(1)}%`);

    if (totalMatches === totalCompare) {
      console.log(`\n🎉 完美匹配！号码5开出时的上期遗漏值算法完全正确！`);
    } else {
      console.log(`\n🔍 存在差异，显示前10个差异:`);
      console.log(`位置\t期号\t\t开奖号码\tExcel\t算法\t差值`);
      console.log(`${'='.repeat(70)}`);

      let diffCount = 0;
      let number5Index = 0;

      for (let i = 0; i < periodsData.length && diffCount < 10 && number5Index < totalCompare; i++) {
        const period = periodsData[i];
        if (!period) continue;

        const uniqueNumbers = [...new Set(period.numbers)];
        if (uniqueNumbers.includes(5)) {
          const excelValue = excelNumber5OpenMissValues[number5Index];
          const algorithmValue = algorithmNumber5OpenMissValues[number5Index];

          if (excelValue !== undefined && algorithmValue !== undefined && excelValue !== algorithmValue) {
            const diff = excelValue - algorithmValue;
            console.log(`${(number5Index + 1).toString().padStart(2)}\t${period.period}\t[${period.numbers.join(',')}]\t\t${excelValue.toString().padStart(2)}\t${algorithmValue.toString().padStart(2)}\t${diff > 0 ? '+' : ''}${diff}`);
            diffCount++;
          }

          number5Index++;
        }
      }
    }



  } catch (error) {
    console.error('❌ 验证失败:', error);
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('🔄 数据库连接已关闭');
    }
  }
}

// 运行验证
if (require.main === module) {
  verifyNumber5MissValues().catch(console.error);
}

export { verifyNumber5MissValues };
