import { AppDataSource } from '../config/database';
import dotenv from 'dotenv';
import path from 'path';

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '../../../.env') });

/**
 * 验证数据一致性
 */
async function verifyDataConsistency() {
  try {
    console.log('🔄 初始化数据库连接...');
    await AppDataSource.initialize();
    console.log('✅ 数据库连接成功');

    // 获取数据库中的1152期数据
    const rawResults = await AppDataSource.query(`
      SELECT period, numbers
      FROM lottery_results
      WHERE lottery_type_id = 4
      AND period >= '3305976'
      AND period <= '3307133'
      ORDER BY period ASC
    `);

    console.log(`📊 数据库中获取到 ${rawResults.length} 期数据`);

    // 检查期号范围
    if (rawResults.length > 0) {
      const firstPeriod = rawResults[0].period;
      const lastPeriod = rawResults[rawResults.length - 1].period;
      console.log(`📋 期号范围: ${firstPeriod} - ${lastPeriod}`);
    }

    // 显示前20期数据对比
    console.log(`\n🎯 前20期数据对比:`);
    console.log(`期号\t\t数据库号码\tExcel格式`);
    console.log(`${'='.repeat(50)}`);

    for (let i = 0; i < Math.min(20, rawResults.length); i++) {
      const row = rawResults[i];
      const period = row.period;
      const dbNumbers = row.numbers; // 如: "4,0,5"
      
      // 转换为Excel格式 (如: "405")
      const excelFormat = dbNumbers.replace(/,/g, '');
      
      console.log(`${period}\t${dbNumbers}\t\t${excelFormat}`);
    }

    // 显示最后20期数据对比
    console.log(`\n🎯 最后20期数据对比:`);
    console.log(`期号\t\t数据库号码\tExcel格式`);
    console.log(`${'='.repeat(50)}`);

    const startIndex = Math.max(0, rawResults.length - 20);
    for (let i = startIndex; i < rawResults.length; i++) {
      const row = rawResults[i];
      const period = row.period;
      const dbNumbers = row.numbers;
      const excelFormat = dbNumbers.replace(/,/g, '');
      
      console.log(`${period}\t${dbNumbers}\t\t${excelFormat}`);
    }

    // 检查特定期号的数据
    const checkPeriods = ['3305976', '3305979', '3305980', '3307131', '3307132', '3307133'];
    console.log(`\n🔍 检查特定期号数据:`);
    
    for (const checkPeriod of checkPeriods) {
      const found = rawResults.find((row: any) => row.period === checkPeriod);
      if (found) {
        const excelFormat = found.numbers.replace(/,/g, '');
        console.log(`期号${checkPeriod}: ${found.numbers} -> ${excelFormat}`);
      } else {
        console.log(`❌ 期号${checkPeriod}: 未找到数据`);
      }
    }

    // 检查数据完整性
    console.log(`\n📊 数据完整性检查:`);
    const expectedCount = 3307133 - 3305976 + 1; // 1158期
    console.log(`期号范围应有期数: ${expectedCount}`);
    console.log(`实际获取期数: ${rawResults.length}`);
    console.log(`数据完整性: ${rawResults.length === expectedCount ? '✅ 完整' : '❌ 不完整'}`);

    if (rawResults.length !== expectedCount) {
      // 找出缺失的期号
      const existingPeriods = new Set(rawResults.map((row: any) => parseInt(row.period)));
      const missingPeriods: number[] = [];
      
      for (let period = 3305976; period <= 3307133; period++) {
        if (!existingPeriods.has(period)) {
          missingPeriods.push(period);
        }
      }
      
      console.log(`❌ 缺失期号数量: ${missingPeriods.length}`);
      if (missingPeriods.length <= 20) {
        console.log(`缺失期号: ${missingPeriods.join(', ')}`);
      } else {
        console.log(`缺失期号(前10个): ${missingPeriods.slice(0, 10).join(', ')}...`);
      }
    }

    // 验证数据格式
    console.log(`\n🔍 数据格式验证:`);
    let formatErrors = 0;
    
    for (let i = 0; i < Math.min(100, rawResults.length); i++) {
      const row = rawResults[i];
      const numbers = row.numbers;
      
      // 检查格式是否为 "数字,数字,数字"
      const parts = numbers.split(',');
      if (parts.length !== 3) {
        console.log(`❌ 期号${row.period}: 格式错误 "${numbers}"`);
        formatErrors++;
      } else {
        // 检查每部分是否为数字
        for (const part of parts) {
          const num = parseInt(part.trim());
          if (isNaN(num) || num < 0 || num > 9) {
            console.log(`❌ 期号${row.period}: 数字错误 "${numbers}"`);
            formatErrors++;
            break;
          }
        }
      }
    }
    
    console.log(`格式错误数量: ${formatErrors}`);
    console.log(`格式验证: ${formatErrors === 0 ? '✅ 正确' : '❌ 有错误'}`);

  } catch (error) {
    console.error('❌ 验证失败:', error);
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('🔄 数据库连接已关闭');
    }
  }
}

// 运行验证
if (require.main === module) {
  verifyDataConsistency().catch(console.error);
}

export { verifyDataConsistency };
