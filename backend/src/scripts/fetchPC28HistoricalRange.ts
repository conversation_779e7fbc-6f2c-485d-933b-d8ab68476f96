import { AppDataSource } from '../config/database';
import { LotteryResult } from '../models/LotteryResult';
import axios from 'axios';
import dotenv from 'dotenv';
import path from 'path';

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '../../../.env') });

interface PC28HistoricalResponse {
  code: number;
  data: {
    list: Array<{
      turnNum: string;
      openNum: string;
      openTime: string;
    }>;
  };
}

/**
 * 获取指定日期的PC28历史数据
 */
async function fetchPC28DataForDate(date: string): Promise<any[]> {
  try {
    const url = `https://23.248.233.80/anls-api/data/capc28/lotteryList/`;
    
    console.log(`🌐 请求 ${date} 的PC28历史数据: ${url}`);
    
    const response = await axios.post(url, {
      gameId: 230,
      date: date,
      pageSize: 500,
      pageNum: 1
    }, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      },
      timeout: 30000
    });

    console.log(`📥 ${date} API响应状态: ${response.status}`);

    if (response.status === 200 && response.data) {
      const apiData = response.data as PC28HistoricalResponse;
      
      if (apiData.code === 200 && apiData.data && apiData.data.list) {
        const results = apiData.data.list.map(item => {
          // 转换UTC时间到东八区
          const utcDate = new Date(item.openTime);
          const chinaDate = new Date(utcDate.getTime() + 8 * 60 * 60 * 1000);
          const drawTime = chinaDate.toISOString().replace('T', ' ').substring(0, 19);
          
          console.log(`🕐 ${date} PC28时间转换: UTC ${utcDate.toISOString()} -> 东八区 ${drawTime}`);
          
          return {
            period: item.turnNum,
            drawTime: drawTime,
            numbers: item.openNum
          };
        });
        
        console.log(`📊 ${date} 解析到 ${results.length} 条PC28数据`);
        return results;
      }
    }
    
    console.log(`⚠️  ${date} 未获取到有效的PC28数据`);
    return [];
    
  } catch (error) {
    console.error(`❌ 获取 ${date} PC28数据失败:`, error);
    return [];
  }
}

/**
 * 批量保存历史数据到数据库
 */
async function batchSaveHistoricalData(data: any[]): Promise<number> {
  if (data.length === 0) {
    return 0;
  }

  try {
    const lotteryResultRepo = AppDataSource.getRepository(LotteryResult);
    
    // 按期号去重
    const uniqueData = data.reduce((acc, current) => {
      const exists = acc.find((item: any) => item.period === current.period);
      if (!exists) {
        acc.push(current);
      }
      return acc;
    }, []);

    console.log(`📊 去重后剩余 ${uniqueData.length} 条数据`);

    // 批量插入
    const entities = uniqueData.map(item => {
      const entity = new LotteryResult();
      entity.lotteryTypeId = 4; // PC28的ID
      entity.period = item.period;
      entity.drawTime = new Date(item.drawTime);
      entity.numbers = item.numbers;
      return entity;
    });

    await lotteryResultRepo.save(entities);
    console.log(`✅ 成功保存 ${entities.length} 条PC28历史数据到数据库`);
    
    return entities.length;
  } catch (error) {
    console.error('❌ 保存PC28历史数据失败:', error);
    return 0;
  }
}

/**
 * 获取日期范围内的所有日期
 */
function getDateRange(startDate: string, endDate: string): string[] {
  const dates: string[] = [];
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
    dates.push(date.toISOString().split('T')[0]);
  }
  
  return dates;
}

/**
 * 主函数：抓取7月1日到7月6日的PC28数据
 */
async function fetchPC28HistoricalRange() {
  try {
    console.log('🔄 初始化数据库连接...');
    await AppDataSource.initialize();
    console.log('✅ 数据库连接成功');

    // 定义日期范围：7月1日到7月6日
    const startDate = '2025-07-01';
    const endDate = '2025-07-06';
    const dates = getDateRange(startDate, endDate);
    
    console.log(`📅 开始抓取PC28历史数据，日期范围: ${startDate} 到 ${endDate}`);
    console.log(`📋 需要抓取的日期: ${dates.join(', ')}`);

    let totalSaved = 0;
    const allData: any[] = [];

    // 循环抓取每一天的数据
    for (const date of dates) {
      console.log(`\n🔄 开始抓取 ${date} 的PC28数据...`);
      
      const dayData = await fetchPC28DataForDate(date);
      
      if (dayData.length > 0) {
        allData.push(...dayData);
        console.log(`✅ ${date} 抓取成功，获得 ${dayData.length} 条数据`);
      } else {
        console.log(`⚠️  ${date} 未获取到数据`);
      }
      
      // 添加延迟避免请求过快
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log(`\n📊 总共抓取到 ${allData.length} 条PC28数据`);

    if (allData.length > 0) {
      // 批量保存到数据库
      totalSaved = await batchSaveHistoricalData(allData);
      
      console.log(`\n✅ PC28历史数据抓取完成！`);
      console.log(`📊 总计保存 ${totalSaved} 条数据到数据库`);
      
      // 检查数据库中的数据统计
      const lotteryResultRepo = AppDataSource.getRepository(LotteryResult);
      const totalCount = await lotteryResultRepo.count({ where: { lotteryTypeId: 4 } });
      
      console.log(`📋 数据库中PC28数据总数: ${totalCount} 条`);
      
      // 获取期号范围
      const minMaxResult = await AppDataSource.query(`
        SELECT MIN(period) as min_period, MAX(period) as max_period
        FROM lottery_results
        WHERE lottery_type_id = 4
      `);
      
      if (minMaxResult.length > 0) {
        console.log(`📈 期号范围: ${minMaxResult[0].min_period} - ${minMaxResult[0].max_period}`);
      }
      
    } else {
      console.log(`❌ 未获取到任何PC28数据`);
    }

  } catch (error) {
    console.error('❌ PC28历史数据抓取失败:', error);
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('🔄 数据库连接已关闭');
    }
  }
}

// 运行脚本
if (require.main === module) {
  fetchPC28HistoricalRange().catch(console.error);
}

export { fetchPC28HistoricalRange };
