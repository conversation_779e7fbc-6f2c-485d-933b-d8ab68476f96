import { AppDataSource } from '../config/database';
import { DataCollectionService } from '../services/DataCollectionService';
import dotenv from 'dotenv';
import path from 'path';

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '../../../.env') });

/**
 * 测试采集窗口时间配置
 */
async function testCollectionWindow() {
  try {
    console.log('🔄 初始化数据库连接...');
    await AppDataSource.initialize();
    console.log('✅ 数据库连接成功');

    // 获取传统彩种信息
    const traditionalLotteries = await AppDataSource.query(`
      SELECT id, code, name, draw_time
      FROM lottery_types
      WHERE code IN ('FC3D', 'PL3', 'PL4')
      AND draw_time IS NOT NULL
    `);

    console.log(`📊 找到 ${traditionalLotteries.length} 个传统彩种:`);
    traditionalLotteries.forEach((lottery: any) => {
      console.log(`   - ${lottery.name} (${lottery.code}): 开奖时间 ${lottery.draw_time}`);
    });

    // 创建数据采集服务实例
    const collectionService = DataCollectionService.getInstance();

    // 测试每个传统彩种的采集窗口判断
    for (const lottery of traditionalLotteries) {
      console.log(`\n🔍 测试 ${lottery.name} 的采集窗口时间...`);
      
      const drawTime = lottery.draw_time; // 如: "21:15:00"
      const [hours, minutes, seconds] = drawTime.split(':').map(Number);
      
      // 构建今天的开奖时间
      const today = new Date();
      const todayDrawTime = new Date(today.getFullYear(), today.getMonth(), today.getDate(), hours, minutes, seconds);
      
      // 构建采集窗口结束时间（开奖后60分钟）
      const collectEndTime = new Date(todayDrawTime.getTime() + 60 * 60 * 1000);
      
      console.log(`   📅 今日开奖时间: ${todayDrawTime.toLocaleString()}`);
      console.log(`   ⏰ 采集窗口结束: ${collectEndTime.toLocaleString()}`);
      console.log(`   🕐 采集窗口时长: 60分钟`);
      
      // 测试不同时间点的采集判断
      const testTimes = [
        { name: '开奖前1小时', offset: -60 },
        { name: '开奖时间', offset: 0 },
        { name: '开奖后30分钟', offset: 30 },
        { name: '开奖后60分钟', offset: 60 },
        { name: '开奖后90分钟', offset: 90 }
      ];
      
      for (const testTime of testTimes) {
        const testDateTime = new Date(todayDrawTime.getTime() + testTime.offset * 60 * 1000);
        const shouldCollect = testDateTime >= todayDrawTime && testDateTime <= collectEndTime;
        
        console.log(`   ${shouldCollect ? '✅' : '❌'} ${testTime.name} (${testDateTime.toLocaleTimeString()}): ${shouldCollect ? '可以采集' : '不可采集'}`);
      }
    }

    console.log(`\n✅ 采集窗口时间测试完成`);
    console.log(`📋 配置总结:`);
    console.log(`   - 传统彩采集窗口: 开奖时间后60分钟内`);
    console.log(`   - 高频彩: 持续采集`);

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('🔄 数据库连接已关闭');
    }
  }
}

/**
 * 测试当前时间是否在采集窗口内
 */
async function testCurrentCollectionStatus() {
  try {
    console.log('\n🔍 测试当前时间的采集状态...');
    
    await AppDataSource.initialize();

    const traditionalLotteries = await AppDataSource.query(`
      SELECT code, name, draw_time
      FROM lottery_types
      WHERE code IN ('FC3D', 'PL3', 'PL4')
      AND draw_time IS NOT NULL
    `);

    const now = new Date();
    console.log(`🕐 当前时间: ${now.toLocaleString()}`);

    for (const lottery of traditionalLotteries) {
      const drawTime = lottery.draw_time;
      const [hours, minutes, seconds] = drawTime.split(':').map(Number);
      
      const today = new Date();
      const todayDrawTime = new Date(today.getFullYear(), today.getMonth(), today.getDate(), hours, minutes, seconds);
      const collectEndTime = new Date(todayDrawTime.getTime() + 60 * 60 * 1000);
      
      const inWindow = now >= todayDrawTime && now <= collectEndTime;
      
      console.log(`${inWindow ? '✅' : '❌'} ${lottery.name}: ${inWindow ? '当前可以采集' : '当前不可采集'}`);
      console.log(`   开奖时间: ${todayDrawTime.toLocaleTimeString()}`);
      console.log(`   窗口结束: ${collectEndTime.toLocaleTimeString()}`);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
    }
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length > 0 && args[0] === 'current') {
    await testCurrentCollectionStatus();
  } else {
    await testCollectionWindow();
  }
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

export { testCollectionWindow, testCurrentCollectionStatus };
