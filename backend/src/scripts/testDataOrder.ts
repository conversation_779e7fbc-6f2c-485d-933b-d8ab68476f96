import { AppDataSource } from '../config/database';
import { MissValuePredictor } from '../predictors/MissValuePredictor';
import dotenv from 'dotenv';
import path from 'path';

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '../../../.env') });

/**
 * 测试数据获取顺序是否正确
 */
async function testDataOrder() {
  try {
    console.log('🔄 初始化数据库连接...');
    await AppDataSource.initialize();
    console.log('✅ 数据库连接成功');

    // 获取排列三的彩种ID
    const lotteryTypes = await AppDataSource.query(`
      SELECT id, code, name
      FROM lottery_types
      WHERE code = 'PL3'
    `);

    if (lotteryTypes.length === 0) {
      console.log('❌ 未找到排列三彩种');
      return;
    }

    const pl3LotteryType = lotteryTypes[0];
    console.log(`📊 测试彩种: ${pl3LotteryType.name} (ID: ${pl3LotteryType.id})`);

    // 创建预测器实例
    const predictor = new (MissValuePredictor as any)(pl3LotteryType.id, 3);

    // 通过反射调用私有方法来测试数据获取
    const getRecentResults = predictor.getRecentResults.bind(predictor);
    
    console.log('\n🔍 测试获取最近16期数据...');
    
    // 直接查询数据库验证
    console.log('\n📋 数据库中最近16期数据（按时间降序）:');
    const dbResults = await AppDataSource.query(`
      SELECT period, numbers, draw_time
      FROM lottery_results
      WHERE lottery_type_id = ?
      ORDER BY draw_time DESC
      LIMIT 16
    `, [pl3LotteryType.id]);

    dbResults.forEach((row: any, index: number) => {
      console.log(`   ${index + 1}. 期号: ${row.period}, 号码: ${row.numbers}, 时间: ${row.draw_time}`);
    });

    console.log('\n📋 数据库中最近16期数据（按期号升序）:');
    const dbResultsSorted = [...dbResults].sort((a: any, b: any) => {
      return parseInt(a.period) - parseInt(b.period);
    });

    dbResultsSorted.forEach((row: any, index: number) => {
      console.log(`   ${index + 1}. 期号: ${row.period}, 号码: ${row.numbers}, 时间: ${row.draw_time}`);
    });

    // 测试预测器的数据获取方法
    console.log('\n🎯 预测器获取的数据顺序:');
    try {
      // 由于getRecentResults是私有方法，我们需要通过其他方式测试
      // 让我们直接测试预测算法，看看数据顺序是否正确
      
      console.log('\n🧮 开始测试预测算法...');
      const result = await predictor.generatePrediction();
      console.log(`✅ 预测结果: ${result.prediction}`);
      
    } catch (error) {
      console.error('❌ 测试预测算法失败:', error);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('🔄 数据库连接已关闭');
    }
  }
}

/**
 * 直接测试数据获取逻辑
 */
async function testDirectDataFetch() {
  try {
    console.log('\n🔍 直接测试数据获取逻辑...');
    
    await AppDataSource.initialize();

    // 获取排列三最近16期数据（修复后的逻辑）
    const results = await AppDataSource.query(`
      SELECT id, lottery_type_id, period, draw_time, numbers
      FROM lottery_results
      WHERE lottery_type_id = (SELECT id FROM lottery_types WHERE code = 'PL3')
      ORDER BY draw_time DESC
      LIMIT 16
    `);

    console.log('\n📊 获取到的数据（按时间降序）:');
    results.forEach((row: any, index: number) => {
      console.log(`   ${index + 1}. 期号: ${row.period}, 号码: ${row.numbers}, 时间: ${row.draw_time}`);
    });

    // 按期号升序排列
    results.sort((a: any, b: any) => {
      const periodA = parseInt(a.period);
      const periodB = parseInt(b.period);
      return periodA - periodB;
    });

    console.log('\n📊 排序后的数据（按期号升序）:');
    results.forEach((row: any, index: number) => {
      console.log(`   ${index + 1}. 期号: ${row.period}, 号码: ${row.numbers}, 时间: ${row.draw_time}`);
    });

    console.log('\n✅ 数据获取逻辑测试完成');

  } catch (error) {
    console.error('❌ 直接测试失败:', error);
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
    }
  }
}

// 主函数
async function main() {
  console.log('🧪 开始测试数据获取顺序...\n');
  
  await testDirectDataFetch();
  await testDataOrder();
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

export { testDataOrder, testDirectDataFetch };
