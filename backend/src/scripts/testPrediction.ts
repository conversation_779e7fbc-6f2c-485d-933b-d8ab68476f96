import { AppDataSource } from '../config/database';
import { MissValuePredictor } from '../predictors/MissValuePredictor';
import { PredictionService } from '../services/PredictionService';
import dotenv from 'dotenv';
import path from 'path';

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '../../../.env') });

/**
 * 胆码预测功能测试脚本
 */
async function testPrediction() {
  try {
    console.log('🔄 初始化数据库连接...');
    await AppDataSource.initialize();
    console.log('✅ 数据库连接成功');

    // 获取所有启用的彩种
    const lotteryTypes = await AppDataSource.query(`
      SELECT id, code, name, type, status
      FROM lottery_types
      WHERE status = 'active'
      ORDER BY id
    `);

    console.log(`📊 找到 ${lotteryTypes.length} 个启用的彩种:`);
    lotteryTypes.forEach((lt: any) => {
      console.log(`   - ${lt.name} (${lt.code}) - ${lt.type}`);
    });

    if (lotteryTypes.length === 0) {
      console.log('⚠️  没有找到启用的彩种，请先初始化数据库');
      return;
    }

    // 测试每个彩种的预测功能
    for (const lotteryType of lotteryTypes) {
      console.log(`\n🎯 测试 ${lotteryType.name} 的胆码预测...`);

      // 检查历史数据量
      const dataCount = await AppDataSource.query(`
        SELECT COUNT(*) as count
        FROM lottery_results
        WHERE lottery_type_id = ?
      `, [lotteryType.id]);

      const count = dataCount[0].count;
      console.log(`📊 ${lotteryType.name} 历史数据量: ${count} 期`);

      if (count < 10) {
        console.log(`⚠️  ${lotteryType.name} 历史数据不足，跳过预测测试`);
        continue;
      }

      // 获取位数
      const positions = getPositionsByType(lotteryType.type);

      // 创建预测器并测试
      const predictor = new MissValuePredictor(lotteryType.id, positions);
      const result = await predictor.generatePrediction();

      if (result.prediction) {
        console.log(`✅ ${lotteryType.name} 预测成功: ${result.prediction}`);
        
        // 测试预测服务
        const predictionService = PredictionService.getInstance();
        await predictionService.generatePredictionForLottery(lotteryType.id);
        
        // 获取保存的预测
        const savedPrediction = await predictionService.getLatestPrediction(lotteryType.id);
        if (savedPrediction) {
          console.log(`💾 ${lotteryType.name} 预测已保存: 期号 ${savedPrediction.target_period}, 胆码 ${savedPrediction.prediction}`);
        }
      } else {
        console.log(`❌ ${lotteryType.name} 预测失败或结果为空`);
      }
    }

    // 测试批量预测
    console.log(`\n🎯 测试批量预测功能...`);
    const predictionService = PredictionService.getInstance();
    await predictionService.generatePredictionsForAllLotteries();

    // 获取预测统计
    console.log(`\n📊 获取预测统计信息...`);
    const stats = await predictionService.getPredictionStats();
    console.log(`总预测数: ${stats.total}`);
    console.log(`今日预测数: ${stats.today}`);
    console.log(`各彩种预测情况:`);
    stats.byLottery.forEach((item: any) => {
      console.log(`   - ${item.lottery_name}: ${item.prediction_count} 条预测, 最新: ${item.latest_prediction || '无'}`);
    });

    console.log(`\n✅ 胆码预测功能测试完成`);

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('🔄 数据库连接已关闭');
    }
  }
}

/**
 * 根据彩种类型获取位数
 */
function getPositionsByType(type: string): number {
  switch (type) {
    case '3star':
      return 3;
    case '4star':
      return 4;
    case '5star':
      return 5;
    default:
      return 3;
  }
}

/**
 * 测试单个彩种的预测算法详细过程
 */
async function testDetailedPrediction(lotteryTypeId: number) {
  console.log(`\n🔍 详细测试彩种 ${lotteryTypeId} 的预测算法...`);

  try {
    // 获取最近10期数据用于演示
    const recentResults = await AppDataSource.query(`
      SELECT period, draw_time, numbers
      FROM lottery_results
      WHERE lottery_type_id = ?
      ORDER BY draw_time DESC
      LIMIT 10
    `, [lotteryTypeId]);

    console.log(`📊 最近10期开奖数据:`);
    recentResults.reverse().forEach((result: any, index: number) => {
      console.log(`   ${index + 1}. 期号: ${result.period}, 号码: ${result.numbers}, 时间: ${result.draw_time}`);
    });

    // 创建预测器
    const predictor = new MissValuePredictor(lotteryTypeId, 3);
    const result = await predictor.generatePrediction();

    console.log(`🎯 预测结果: ${result.prediction}`);

  } catch (error) {
    console.error('❌ 详细测试失败:', error);
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length > 0 && args[0] === 'detail') {
    // 详细测试模式
    const lotteryTypeId = parseInt(args[1] || '1');
    
    console.log('🔄 初始化数据库连接...');
    await AppDataSource.initialize();
    console.log('✅ 数据库连接成功');
    
    await testDetailedPrediction(lotteryTypeId);
    
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
    }
  } else {
    // 常规测试模式
    await testPrediction();
  }
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

export { testPrediction, testDetailedPrediction };
