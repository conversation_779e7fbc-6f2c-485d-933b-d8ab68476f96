import { AppDataSource } from '../config/database';
import dotenv from 'dotenv';
import path from 'path';

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '../../../.env') });

interface LotteryResult {
  period: string;
  drawTime: string;
  numbers: number[];
}

interface PeriodMissData {
  period: string;
  numbers: number[];
  missValues: number[];
}

/**
 * 解析号码字符串
 */
function parseNumbers(numbersStr: string): number[] {
  if (!numbersStr) return [];
  
  // 处理不同格式的号码字符串
  const cleanStr = numbersStr.replace(/[,，\s]/g, '');
  return cleanStr.split('').map(char => parseInt(char, 10)).filter(num => !isNaN(num));
}

/**
 * 计算所有期数的遗漏值
 */
function calculateAllMissValues(results: LotteryResult[]): PeriodMissData[] {
  const periodsData: PeriodMissData[] = [];

  for (let i = 0; i < results.length; i++) {
    const currentResult = results[i];
    if (!currentResult) continue;

    const missValues = new Array(10).fill(0);

    // 计算每个号码(0-9)在当前期的遗漏值
    for (let digit = 0; digit <= 9; digit++) {
      let missCount = 0;

      // 从当前期往前查找该数字上次出现的位置
      for (let j = i - 1; j >= 0; j--) {
        const prevResult = results[j];
        if (!prevResult) continue;

        const prevNumbers = parseNumbers(prevResult.numbers.join(''));

        if (prevNumbers.includes(digit)) {
          break; // 找到该数字，停止计数
        }

        missCount++;
      }

      missValues[digit] = missCount;
    }

    periodsData.push({
      period: currentResult.period,
      numbers: parseNumbers(currentResult.numbers.join('')),
      missValues: missValues
    });
  }

  return periodsData;
}

/**
 * 构建遗漏值数组
 */
function buildMissValueArrays(periodsData: PeriodMissData[]): Map<number, number[]> {
  const missValueArrays = new Map<number, number[]>();

  // 初始化每个号码的遗漏值数组
  for (let digit = 0; digit <= 9; digit++) {
    missValueArrays.set(digit, []);
  }

  // 从第二期开始处理（第一期没有上期遗漏值）
  for (let i = 1; i < periodsData.length; i++) {
    const currentPeriod = periodsData[i];
    const prevPeriod = periodsData[i - 1];

    if (!currentPeriod || !prevPeriod) continue;

    // 检查当前期开出的号码
    for (const digit of currentPeriod.numbers) {
      // 如果该号码在当前期开出，记录上期该号码的遗漏值
      const prevMissValue = prevPeriod.missValues[digit];
      if (prevMissValue !== undefined) {
        const missArray = missValueArrays.get(digit)!;
        missArray.push(prevMissValue);
      }
    }
  }

  return missValueArrays;
}

/**
 * 验证遗漏值计算
 */
async function verifyMissValueCalculation() {
  try {
    console.log('🔄 初始化数据库连接...');
    await AppDataSource.initialize();
    console.log('✅ 数据库连接成功');

    // 获取PC28最近1152期数据
    const rawResults = await AppDataSource.query(`
      SELECT period, draw_time, numbers
      FROM lottery_results
      WHERE lottery_type_id = 4
      ORDER BY period DESC
      LIMIT 1152
    `);

    // 反转数组，使其按期号升序排列
    rawResults.reverse();

    console.log(`📊 获取到 ${rawResults.length} 期PC28历史数据`);

    // 转换数据格式
    const results: LotteryResult[] = rawResults.map((row: any) => ({
      period: row.period,
      drawTime: row.draw_time,
      numbers: parseNumbers(row.numbers)
    }));

    // 计算遗漏值
    const periodsData = calculateAllMissValues(results);
    console.log(`📊 计算了 ${periodsData.length} 期的遗漏值`);

    // 构建遗漏值数组
    const missValueArrays = buildMissValueArrays(periodsData);

    // 输出号码0的详细信息
    const digit0Array = missValueArrays.get(0)!;
    console.log(`\n🎯 号码0的遗漏值数组:`);
    console.log(`   数组长度: ${digit0Array.length}`);
    console.log(`   数组内容: [${digit0Array.join(',')}]`);

    // 统计遗漏值0的出现次数
    const zeroCount = digit0Array.filter(val => val === 0).length;
    console.log(`   遗漏值0出现次数: ${zeroCount}`);

    // 显示最后几期的详细信息
    console.log(`\n📋 最后10期的详细信息:`);
    const lastTenPeriods = periodsData.slice(-10);
    lastTenPeriods.forEach((period) => {
      console.log(`   期号${period.period}: 号码[${period.numbers.join(',')}], 号码0遗漏值=${period.missValues[0]}`);
    });

    // 获取当前最新期的信息
    const latestPeriod = periodsData[periodsData.length - 1];
    if (latestPeriod) {
      console.log(`\n🎯 最新期信息:`);
      console.log(`   期号: ${latestPeriod.period}`);
      console.log(`   开奖号码: [${latestPeriod.numbers.join(',')}]`);
      console.log(`   当前遗漏值: ${latestPeriod.missValues.map((miss, digit) => `${digit}:${miss}`).join(', ')}`);
    }

    // 验证您的Excel数据
    console.log(`\n📊 与Excel数据对比:`);
    console.log(`   Excel统计: 号码0遗漏值数组有76个元素`);
    console.log(`   算法计算: 号码0遗漏值数组有${digit0Array.length}个元素`);
    console.log(`   Excel统计: 遗漏值0出现76次`);
    console.log(`   算法计算: 遗漏值0出现${zeroCount}次`);

    if (digit0Array.length === 76 && zeroCount === 76) {
      console.log(`   ✅ 算法计算结果与Excel数据一致`);
    } else {
      console.log(`   ❌ 算法计算结果与Excel数据不一致`);
    }

  } catch (error) {
    console.error('❌ 验证失败:', error);
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('🔄 数据库连接已关闭');
    }
  }
}

// 运行验证
if (require.main === module) {
  verifyMissValueCalculation().catch(console.error);
}

export { verifyMissValueCalculation };
