import { AppDataSource } from '../config/database';
import dotenv from 'dotenv';
import path from 'path';

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '../../../.env') });

// Excel前20期的遗漏值
const excelMissValues = [0,1,2,0,0,1,2,0,1,2,3,4,5,0,1,2,3,4,5,0];

/**
 * 解析号码字符串
 */
function parseNumbers(numbersStr: string): number[] {
  if (!numbersStr) return [];
  
  if (numbersStr.includes(',')) {
    return numbersStr.split(',').map(n => parseInt(n.trim())).filter(num => !isNaN(num));
  } else {
    return numbersStr.split('').map(n => parseInt(n)).filter(num => !isNaN(num));
  }
}

/**
 * 详细分析前20期的遗漏值计算
 */
async function detailedMissAnalysis() {
  try {
    console.log('🔄 初始化数据库连接...');
    await AppDataSource.initialize();
    console.log('✅ 数据库连接成功');

    // 获取前20期数据
    const rawResults = await AppDataSource.query(`
      SELECT period, numbers
      FROM lottery_results
      WHERE lottery_type_id = 4
      AND period >= '3305976'
      ORDER BY period ASC
      LIMIT 20
    `);

    console.log(`📊 获取到 ${rawResults.length} 期数据`);

    console.log(`\n🎯 前20期详细分析:`);
    console.log(`位置\t期号\t\t开奖号码\t包含0\tExcel遗漏\t分析`);
    console.log(`${'='.repeat(80)}`);

    for (let i = 0; i < Math.min(20, rawResults.length); i++) {
      const row = rawResults[i];
      const period = row.period;
      const numbersStr = row.numbers;
      const numbers = parseNumbers(numbersStr);
      const contains0 = numbers.includes(0);
      const excelMiss = excelMissValues[i];

      if (excelMiss === undefined) continue;

      let analysis = '';

      if (i === 0) {
        analysis = '第1期，Excel=0（初始值）';
      } else if (contains0) {
        analysis = '开出0，Excel=0';
      } else {
        // 查找上次开出0的位置
        let lastZeroIndex = -1;
        for (let j = i - 1; j >= 0; j--) {
          const prevRow = rawResults[j];
          const prevNumbers = parseNumbers(prevRow.numbers);
          if (prevNumbers.includes(0)) {
            lastZeroIndex = j;
            break;
          }
        }
        
        if (lastZeroIndex >= 0) {
          const distance = i - lastZeroIndex;
          analysis = `距离第${lastZeroIndex + 1}期开出0有${distance}期`;
        } else {
          analysis = `从第1期到现在都没开出0，距离${i + 1}期`;
        }
      }
      
      const position = i + 1;
      const containsStr = contains0 ? '是' : '否';
      
      console.log(`${position.toString().padStart(2)}\t${period}\t[${numbers.join(',')}]\t\t${containsStr}\t${excelMiss.toString().padStart(2)}\t\t${analysis}`);
    }

    // 手动验证Excel逻辑
    console.log(`\n🔍 手动验证Excel逻辑:`);
    
    // 假设Excel的逻辑是：遗漏值 = 当前期位置 - 上次开出0的期位置
    console.log(`\n方案1: 遗漏值 = 当前期位置 - 上次开出0的期位置`);
    for (let i = 0; i < Math.min(10, rawResults.length); i++) {
      const row = rawResults[i];
      const numbers = parseNumbers(row.numbers);
      const contains0 = numbers.includes(0);
      const excelMiss = excelMissValues[i];
      
      let calculatedMiss = 0;
      
      if (contains0) {
        calculatedMiss = 0;
      } else {
        // 查找上次开出0的位置
        let lastZeroIndex = -1;
        for (let j = i - 1; j >= 0; j--) {
          const prevRow = rawResults[j];
          const prevNumbers = parseNumbers(prevRow.numbers);
          if (prevNumbers.includes(0)) {
            lastZeroIndex = j;
            break;
          }
        }
        
        if (lastZeroIndex >= 0) {
          calculatedMiss = i - lastZeroIndex;
        } else {
          calculatedMiss = i + 1; // 从第1期开始计数
        }
      }
      
      const match = calculatedMiss === excelMiss ? '✅' : '❌';
      console.log(`第${i+1}期: Excel=${excelMiss}, 计算=${calculatedMiss} ${match}`);
    }

    // 假设Excel的第1期遗漏值是特殊处理的
    console.log(`\n方案2: 第1期特殊处理，其他期正常计算`);
    for (let i = 0; i < Math.min(10, rawResults.length); i++) {
      const row = rawResults[i];
      const numbers = parseNumbers(row.numbers);
      const contains0 = numbers.includes(0);
      const excelMiss = excelMissValues[i];
      
      let calculatedMiss = 0;
      
      if (i === 0) {
        // 第1期：如果包含0则为0，否则为1
        calculatedMiss = contains0 ? 0 : 1;
      } else if (contains0) {
        calculatedMiss = 0;
      } else {
        // 查找上次开出0的位置
        let lastZeroIndex = -1;
        for (let j = i - 1; j >= 0; j--) {
          const prevRow = rawResults[j];
          const prevNumbers = parseNumbers(prevRow.numbers);
          if (prevNumbers.includes(0)) {
            lastZeroIndex = j;
            break;
          }
        }
        
        if (lastZeroIndex >= 0) {
          calculatedMiss = i - lastZeroIndex;
        } else {
          calculatedMiss = i + 1;
        }
      }
      
      const match = calculatedMiss === excelMiss ? '✅' : '❌';
      console.log(`第${i+1}期: Excel=${excelMiss}, 计算=${calculatedMiss} ${match}`);
    }

  } catch (error) {
    console.error('❌ 分析失败:', error);
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('🔄 数据库连接已关闭');
    }
  }
}

// 运行分析
if (require.main === module) {
  detailedMissAnalysis().catch(console.error);
}

export { detailedMissAnalysis };
