# 彩票分析平台开发文档

## 📋 项目概述

### 项目简介
彩票分析平台是一个基于历史数据分析的彩票预测系统，支持多种彩票类型的数据采集、胆码预测和过滤分析功能。

### 核心功能
- **数据采集**: 自动采集6种彩票的开奖数据
- **胆码预测**: 基于遗漏值频次分析的预测算法
- **过滤分析**: 提供多种过滤条件筛选号码组合
- **用户管理**: 支持会员权限和单点登录
- **管理后台**: 完整的后台管理功能

### 支持彩种
1. **福彩3D** - 传统彩，每日21:15开奖
2. **排列三** - 传统彩，每日21:30开奖（使用排列五数据前3位）
3. **排列四** - 传统彩，每日21:30开奖（使用排列五数据前4位）
4. **加拿大PC28** - 高频彩，每210秒开奖
5. **幸运五星彩前四** - 高频彩，每300秒开奖

## 🏗️ 系统架构

### 技术栈
- **前端**: 原生JavaScript + CSS3 + HTML5
- **后端**: Node.js + TypeScript + Express
- **数据库**: MySQL 8.0
- **部署**: 宝塔面板 + Nginx + PM2
- **服务器**: Debian 12

### 架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户前端      │    │   管理前端      │    │   数据采集器    │
│  (移动优先)     │    │  (PC管理端)     │    │  (定时采集)     │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │      后端API服务          │
                    │   (Express + TypeScript)  │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │      MySQL数据库          │
                    │   (开奖数据 + 预测结果)   │
                    └───────────────────────────┘
```

### 数据流向
```
数据源 → 采集器 → 数据库 → 预测算法 → API → 前端显示
  ↓        ↓        ↓        ↓       ↓      ↓
TXT/API  定时采集  存储清理  胆码生成  权限验证  实时更新
```

## 📁 项目结构

```
member_prediction/
├── docs/                    # 开发文档
│   ├── README.md           # 项目概述 (本文件)
│   ├── frontend-user.md    # 用户前端文档
│   ├── frontend-admin.md   # 管理前端文档
│   ├── backend.md          # 后端开发文档
│   ├── database.md         # 数据库设计文档
│   └── deployment.md       # 部署运维文档
├── frontend/               # 前端代码
│   ├── user/              # 用户端
│   │   ├── index.html     # 首页
│   │   ├── analysis.html  # 分析页面
│   │   ├── profile.html   # 用户资料
│   │   ├── css/           # 样式文件
│   │   └── js/            # JavaScript文件
│   └── admin/             # 管理端
│       ├── index.html     # 管理首页
│       ├── css/           # 管理端样式
│       └── js/            # 管理端脚本
├── backend/               # 后端代码
│   ├── src/               # 源代码
│   │   ├── controllers/   # 控制器
│   │   ├── services/      # 业务服务
│   │   ├── models/        # 数据模型
│   │   ├── collectors/    # 数据采集器
│   │   ├── predictors/    # 预测算法
│   │   ├── routes/        # 路由定义
│   │   ├── middleware/    # 中间件
│   │   └── utils/         # 工具函数
│   ├── package.json       # 依赖配置
│   └── tsconfig.json      # TypeScript配置
├── database-init.sql      # 数据库初始化脚本
├── ecosystem.config.js    # PM2配置文件
├── nginx.conf.template    # Nginx配置模板
└── .env.example          # 环境变量示例
```

## 📊 开发进度

### 已完成功能 (90%)
- ✅ 数据库设计和初始化脚本
- ✅ 数据采集器架构和完整实现
  - ✅ 福彩3D/排列三/排列四数据采集 (TXT格式)
  - ✅ PC28/幸运五星彩数据采集 (API接口)
  - ✅ 数据格式统一和自动转换
  - ✅ 历史数据批量初始化
  - ✅ 实时数据采集和更新
- ✅ 胆码预测算法完整实现
- ✅ 开奖时间计算系统
- ✅ 用户认证和权限系统设计
- ✅ 前端页面结构和组件设计
- ✅ 管理后台功能设计和API文档
  - ✅ 彩种数据管理API (重置/采集/初始化)
  - ✅ 批量操作和服务控制
  - ✅ 错误处理和状态管理
  - ✅ UI组件和样式指南
- ✅ 部署方案和运维文档

### 待开发功能 (10%)
- 🔄 前端JavaScript功能实现
- 🔄 管理后台界面实现
- 🔄 系统集成测试

### 开发优先级
1. **第一阶段**: 后端API + 数据采集器
2. **第二阶段**: 用户前端 + 基础功能
3. **第三阶段**: 管理后台 + 高级功能
4. **第四阶段**: 系统优化 + 性能调优

## 🎯 核心特性

### 胆码预测算法
- **算法原理**: 基于遗漏值频次分析
- **数据基础**: 最近1000期历史数据
- **预测精度**: 通过历史验证优化
- **更新机制**: 每次新开奖后自动重新计算

### 过滤分析系统
- **号码生成**: 自动生成所有可能组合
- **过滤条件**: 支持多种数学条件筛选
- **实时筛选**: 前端实时计算，无需后端通讯
- **状态保护**: 用户过滤条件在数据更新时保持不变

### 用户权限系统
- **单点登录**: 防止多端同时登录
- **会员权限**: 按彩种分别控制访问权限
- **自动踢线**: 新设备登录时自动踢下线旧设备
- **权限验证**: 所有敏感操作都需要权限验证

## 📚 文档说明

### 文档结构
每个文档都包含完整的实现细节，确保任何开发者都能独立完成对应模块的开发。

### 阅读顺序
1. **README.md** - 项目总览 (本文件)
2. **database.md** - 数据库设计
3. **backend.md** - 后端开发
4. **frontend-user.md** - 用户前端
5. **frontend-admin.md** - 管理前端
6. **deployment.md** - 部署运维

### 开发规范
- 所有代码必须有详细注释
- 遵循TypeScript/JavaScript最佳实践
- 前端必须支持移动端优先
- 后端必须包含完整的错误处理
- 数据库操作必须使用事务保证一致性

## 🚀 快速开始

### 环境要求
- **服务器**: Debian 12
- **Node.js**: >= 18.0.0
- **MySQL**: >= 8.0
- **内存**: >= 2GB

### 部署步骤
1. 按照 `deployment.md` 配置服务器环境
2. 使用 `database-init.sql` 初始化数据库
3. 配置环境变量和数据源
4. 启动后端服务和数据采集器
5. 部署前端文件到Web目录

详细部署说明请参考 `docs/deployment.md`。

---

**注意**: 本文档确保任何开发者拿到后都能做出功能完全一致的产品。如有疑问，请查阅对应的详细文档。


## 🚀 开发进度

### ✅ 已完成功能

#### 1. 🗄️ 数据库连接和数据模型 (100% 完成)
- ✅ 连接 MySQL 数据库
  - 配置 TypeORM 数据库连接
  - 实现数据库连接测试功能
  - 集成到主应用程序启动流程
- ✅ 创建彩种数据表
  - 设计完整的数据模型结构
  - 匹配现有数据库表结构
  - 支持5种彩票类型数据存储
- ✅ 实现数据持久化
  - 创建数据库初始化脚本
  - 成功插入基础彩种数据
  - 验证数据完整性

**技术实现：**
- 数据库配置文件：`backend/src/config/database.ts`
- 数据模型：`backend/src/models/`
  - LotteryType（彩种类型）
  - LotteryResult（开奖结果）
  - Prediction（预测结果）
  - User（用户）
  - UserMembership（用户会员权限）
- 初始化脚本：`backend/src/scripts/initDatabase.ts`

**数据库状态：**
```
+-------+-----------------------+-------+--------+
| code  | name                  | type  | status |
+-------+-----------------------+-------+--------+
| FC3D  | 福彩3D                | 3star | active |
| PL3   | 排列三                | 3star | active |
| PL4   | 排列四                | 4star | active |
| PC28  | 加拿大PC28            | 3star | active |
| XYWXC | 幸运五星彩前四        | 4star | active |
+-------+-----------------------+-------+--------+
```

### 🔄 下一步开发计划

#### 2. 📊 数据采集系统 (100% 完成)
- ✅ 实现彩票数据自动采集
  - 创建 DataCollectionService 单例服务
  - 支持多彩种并发采集
  - 实现定时采集和手动触发机制
- ✅ 支持多种数据源 (TXT文件、API接口)
  - TXT文件数据源解析（已测试）
  - API接口数据源框架（已准备）
  - 排列三/四共用数据源支持
- ✅ 定时采集和实时更新
  - 可配置采集间隔
  - 自动去重机制
  - 实时状态监控

**技术实现：**
- 数据采集服务：`backend/src/services/DataCollectionService.ts`
- API接口：`backend/src/routes/collection.ts`
- 数据源配置：数据库 lottery_types 表
- 支持的数据格式：排列五TXT格式解析

**采集数据统计：**
- 排列三 (PL3): 7,299条历史数据
- 排列四 (PL4): 4,631条历史数据
- 数据源: https://data.17500.cn/pl5_asc.txt
- 最新数据: 2025-07-04期

**API接口：**
- `GET /api/collection/status` - 获取采集状态
- `POST /api/collection/start` - 启动采集服务
- `POST /api/collection/stop` - 停止采集服务
- `POST /api/collection/manual/:lotteryCode` - 手动触发采集

**胆码预测API**:
- `GET /api/prediction/lottery/:lotteryId/latest` - 获取最新预测
- `GET /api/prediction/lottery/:lotteryId/history` - 获取历史预测
- `GET /api/prediction/lottery/:lotteryId/period/:period/comparison` - 预测对比
- `POST /api/prediction/lottery/:lotteryId/generate` - 手动生成预测
- `POST /api/prediction/generate-all` - 批量生成预测
- `GET /api/prediction/stats` - 预测统计信息

#### 3. 🧠 胆码预测算法 (已完成)
- ✅ 基于遗漏值频次分析的预测算法
- ✅ 使用最近1000期历史数据
- ✅ 自动生成预测结果
- ✅ 新数据入库时自动触发预测
- ✅ 支持所有彩种的胆码预测

#### 4. 👤 用户认证系统 (待开发)
- ⏳ 登录注册功能
- ⏳ 会员权限管理
- ⏳ 单点登录机制

#### 5. 🎛️ 管理后台 (待开发)
- ⏳ 用户管理
- ⏳ 彩种管理
- ⏳ 系统监控